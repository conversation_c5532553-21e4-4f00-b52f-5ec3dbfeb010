-- =====================================================================================
-- COMPREHENSIVE ACADEMIC MANAGEMENT SYSTEM
-- =====================================================================================
-- This system handles trades, subjects, room allocation, timetables, and evaluation
-- with proper academic hierarchy and grade calculation rules
-- =====================================================================================

-- =====================================================================================
-- SECTION 1: INFRASTRUCTURE AND ROOM MANAGEMENT
-- =====================================================================================

-- Create rooms table for the 20 classrooms
CREATE TABLE IF NOT EXISTS rooms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_number VARCHAR(10) NOT NULL UNIQUE,
    room_name VARCHAR(100),
    capacity INT DEFAULT 50,
    room_type ENUM('classroom', 'laboratory', 'library', 'auditorium') DEFAULT 'classroom',
    facilities TEXT, -- JSON or comma-separated list of facilities
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the 20 classrooms
INSERT IGNORE INTO rooms (room_number, room_name, capacity, room_type) VALUES
('R001', 'Classroom 1', 50, 'classroom'),
('R002', 'Classroom 2', 50, 'classroom'),
('R003', 'Classroom 3', 50, 'classroom'),
('R004', 'Classroom 4', 50, 'classroom'),
('R005', 'Classroom 5', 50, 'classroom'),
('R006', 'Classroom 6', 50, 'classroom'),
('R007', 'Classroom 7', 50, 'classroom'),
('R008', 'Classroom 8', 50, 'classroom'),
('R009', 'Classroom 9', 50, 'classroom'),
('R010', 'Classroom 10', 50, 'classroom'),
('R011', 'Classroom 11', 50, 'classroom'),
('R012', 'Classroom 12', 50, 'classroom'),
('R013', 'Classroom 13', 50, 'classroom'),
('R014', 'Classroom 14', 50, 'classroom'),
('R015', 'Classroom 15', 50, 'classroom'),
('R016', 'Classroom 16', 50, 'classroom'),
('R017', 'Classroom 17', 50, 'classroom'),
('R018', 'Classroom 18', 50, 'classroom'),
('R019', 'Classroom 19', 50, 'classroom'),
('R020', 'Classroom 20', 50, 'classroom');

-- Create trades table with proper trade definitions
CREATE TABLE IF NOT EXISTS academic_trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_code VARCHAR(20) NOT NULL UNIQUE,
    trade_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the three trades for classes 11 and 12
INSERT IGNORE INTO academic_trades (trade_code, trade_name, description) VALUES
('MED', 'Medical', 'Medical stream with Biology focus'),
('NON_MED', 'Non-Medical', 'Non-Medical stream with Mathematics focus'),
('COMM', 'Commerce', 'Commerce stream with Business focus');

-- Create sections table
CREATE TABLE IF NOT EXISTS academic_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_code VARCHAR(10) NOT NULL,
    section_name VARCHAR(50) NOT NULL,
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_section_code (section_code)
);

-- Insert sections A through J (to cover 6 sections for Non-Medical)
INSERT IGNORE INTO academic_sections (section_code, section_name, display_order) VALUES
('A', 'Section A', 1),
('B', 'Section B', 2),
('C', 'Section C', 3),
('D', 'Section D', 4),
('E', 'Section E', 5),
('F', 'Section F', 6),
('G', 'Section G', 7),
('H', 'Section H', 8),
('I', 'Section I', 9),
('J', 'Section J', 10);

-- Create class-trade-section combinations table
CREATE TABLE IF NOT EXISTS class_trade_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    trade_id INT NOT NULL,
    section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    total_students INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (trade_id) REFERENCES academic_trades(id),
    FOREIGN KEY (section_id) REFERENCES academic_sections(id),
    UNIQUE KEY unique_class_trade_section (class_level, trade_id, section_id, academic_session),
    INDEX idx_session_class (academic_session, class_level)
);

-- Create room allocation tracking system
CREATE TABLE IF NOT EXISTS room_allocations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    room_id INT NOT NULL,
    class_trade_section_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    allocation_start_date DATE,
    allocation_end_date DATE,
    allocated_by INT, -- user who made the allocation
    allocation_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id),
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id),
    FOREIGN KEY (allocated_by) REFERENCES users(id),
    INDEX idx_session_allocation (academic_session, is_active),
    INDEX idx_room_session (room_id, academic_session, is_active)
);

-- =====================================================================================
-- SECTION 2: SUBJECT STRUCTURE AND CURRICULUM
-- =====================================================================================

-- Enhanced subjects table with categorization
CREATE TABLE IF NOT EXISTS academic_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_code VARCHAR(20) NOT NULL UNIQUE,
    subject_name VARCHAR(100) NOT NULL,
    subject_category ENUM('compulsory_core', 'selective', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    theory_weightage DECIMAL(5,2) DEFAULT 70.00, -- percentage weightage for theory
    practical_weightage DECIMAL(5,2) DEFAULT 20.00, -- percentage weightage for practical
    cce_weightage DECIMAL(5,2) DEFAULT 10.00, -- percentage weightage for CCE
    max_theory_marks DECIMAL(6,2) DEFAULT 70.00,
    max_practical_marks DECIMAL(6,2) DEFAULT 20.00,
    max_cce_marks DECIMAL(6,2) DEFAULT 10.00,
    total_max_marks DECIMAL(6,2) DEFAULT 100.00,
    include_in_grand_total BOOLEAN DEFAULT TRUE, -- CRITICAL: Controls if subject is included in grand total
    subject_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert all subjects with proper categorization
INSERT IGNORE INTO academic_subjects (subject_code, subject_name, subject_category, theory_weightage, practical_weightage, cce_weightage, max_theory_marks, max_practical_marks, max_cce_marks, include_in_grand_total, subject_order) VALUES
-- Compulsory Core Subjects
('PHY', 'Physics', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 1),
('CHEM', 'Chemistry', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 2),
('MATH', 'Mathematics', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 3),
('BIO', 'Biology', 'compulsory_core', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 4),
('ACC', 'Accountancy', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 5),
('BS', 'Business Studies', 'compulsory_core', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 6),

-- Selective Subjects
('ECO', 'Economics', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 7),
('MOP', 'Methods of Production', 'selective', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, TRUE, 8),
('EBUS', 'E-Business', 'selective', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 9),

-- Compulsory Language Subjects
('PUN', 'Punjabi', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 10),
('ENG', 'English', 'compulsory_language', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, TRUE, 11),

-- Additional Compulsory Subjects (NOT included in grand total)
('CS', 'Computer Science', 'additional_compulsory', 60.00, 30.00, 10.00, 60.00, 30.00, 10.00, FALSE, 12),
('EVS', 'Environmental Science', 'additional_compulsory', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 13),

-- Optional Subjects (NOT included in grand total)
('BIO_OPT', 'Biology (Optional)', 'optional', 70.00, 20.00, 10.00, 70.00, 20.00, 10.00, FALSE, 14),
('MATH_OPT', 'Mathematics (Optional)', 'optional', 80.00, 10.00, 10.00, 80.00, 10.00, 10.00, FALSE, 15);

-- Create trade-subject combinations table
CREATE TABLE IF NOT EXISTS trade_subject_combinations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_id INT NOT NULL,
    subject_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL, -- '11' or '12'
    subject_type ENUM('compulsory_core', 'selective_option', 'compulsory_language', 'additional_compulsory', 'optional') NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    academic_session VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trade_id) REFERENCES academic_trades(id),
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    UNIQUE KEY unique_trade_subject_class (trade_id, subject_id, class_level, academic_session),
    INDEX idx_trade_class_session (trade_id, class_level, academic_session)
);

-- Insert trade-subject combinations for 2023-2024 session

-- Medical Trade Combinations
INSERT IGNORE INTO trade_subject_combinations (trade_id, subject_id, class_level, subject_type, is_mandatory, academic_session) VALUES
-- Medical Class 11 & 12 - Compulsory Core
(1, 1, '11', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(1, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(1, 2, '11', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(1, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(1, 4, '11', 'compulsory_core', TRUE, '2023-2024'), -- Biology
(1, 4, '12', 'compulsory_core', TRUE, '2023-2024'), -- Biology

-- Medical - Selective Subjects (Class 11: Economics OR MOP, Class 12: Economics OR E-Business)
(1, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(1, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(1, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(1, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Medical - Compulsory Language
(1, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(1, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(1, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(1, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Medical - Additional Compulsory
(1, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(1, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(1, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(1, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Medical - Optional
(1, 15, '11', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)
(1, 15, '12', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)

-- Non-Medical Trade Combinations
-- Non-Medical Class 11 & 12 - Compulsory Core
(2, 1, '11', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(2, 1, '12', 'compulsory_core', TRUE, '2023-2024'), -- Physics
(2, 2, '11', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(2, 2, '12', 'compulsory_core', TRUE, '2023-2024'), -- Chemistry
(2, 3, '11', 'compulsory_core', TRUE, '2023-2024'), -- Mathematics
(2, 3, '12', 'compulsory_core', TRUE, '2023-2024'), -- Mathematics

-- Non-Medical - Selective Subjects
(2, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(2, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(2, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(2, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Non-Medical - Compulsory Language
(2, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(2, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(2, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(2, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Non-Medical - Additional Compulsory
(2, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(2, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(2, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(2, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Non-Medical - Optional
(2, 14, '11', 'optional', FALSE, '2023-2024'), -- Biology (Optional)
(2, 14, '12', 'optional', FALSE, '2023-2024'), -- Biology (Optional)

-- Commerce Trade Combinations
-- Commerce Class 11 & 12 - Compulsory Core
(3, 5, '11', 'compulsory_core', TRUE, '2023-2024'), -- Accountancy
(3, 5, '12', 'compulsory_core', TRUE, '2023-2024'), -- Accountancy
(3, 6, '11', 'compulsory_core', TRUE, '2023-2024'), -- Business Studies
(3, 6, '12', 'compulsory_core', TRUE, '2023-2024'), -- Business Studies

-- Commerce - Selective Subjects
(3, 7, '11', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 11)
(3, 8, '11', 'selective_option', FALSE, '2023-2024'), -- MOP (Class 11)
(3, 7, '12', 'selective_option', FALSE, '2023-2024'), -- Economics (Class 12)
(3, 9, '12', 'selective_option', FALSE, '2023-2024'), -- E-Business (Class 12)

-- Commerce - Compulsory Language
(3, 10, '11', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(3, 10, '12', 'compulsory_language', TRUE, '2023-2024'), -- Punjabi
(3, 11, '11', 'compulsory_language', TRUE, '2023-2024'), -- English
(3, 11, '12', 'compulsory_language', TRUE, '2023-2024'), -- English

-- Commerce - Additional Compulsory
(3, 12, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(3, 12, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Computer Science
(3, 13, '11', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science
(3, 13, '12', 'additional_compulsory', TRUE, '2023-2024'), -- Environmental Science

-- Commerce - Optional
(3, 15, '11', 'optional', FALSE, '2023-2024'), -- Mathematics (Optional)
(3, 15, '12', 'optional', FALSE, '2023-2024'); -- Mathematics (Optional)

-- =====================================================================================
-- SECTION 3: TIMETABLE AND SCHEDULING SYSTEM
-- =====================================================================================

-- Create time slots table
CREATE TABLE IF NOT EXISTS time_slots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    slot_name VARCHAR(50) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    slot_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert standard time slots
INSERT IGNORE INTO time_slots (slot_name, start_time, end_time, slot_order) VALUES
('Period 1', '08:00:00', '08:45:00', 1),
('Period 2', '08:45:00', '09:30:00', 2),
('Period 3', '09:30:00', '10:15:00', 3),
('Break', '10:15:00', '10:30:00', 4),
('Period 4', '10:30:00', '11:15:00', 5),
('Period 5', '11:15:00', '12:00:00', 6),
('Period 6', '12:00:00', '12:45:00', 7),
('Lunch', '12:45:00', '13:30:00', 8),
('Period 7', '13:30:00', '14:15:00', 9),
('Period 8', '14:15:00', '15:00:00', 10);

-- Create days table
CREATE TABLE IF NOT EXISTS weekdays (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day_name VARCHAR(20) NOT NULL,
    day_short VARCHAR(3) NOT NULL,
    day_order INT DEFAULT 0,
    is_working_day BOOLEAN DEFAULT TRUE
);

-- Insert weekdays
INSERT IGNORE INTO weekdays (day_name, day_short, day_order, is_working_day) VALUES
('Monday', 'MON', 1, TRUE),
('Tuesday', 'TUE', 2, TRUE),
('Wednesday', 'WED', 3, TRUE),
('Thursday', 'THU', 4, TRUE),
('Friday', 'FRI', 5, TRUE),
('Saturday', 'SAT', 6, TRUE),
('Sunday', 'SUN', 7, FALSE);

-- Create timetable table
CREATE TABLE IF NOT EXISTS class_timetables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_trade_section_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT,
    room_id INT,
    day_id INT NOT NULL,
    time_slot_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    effective_from DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id),
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    FOREIGN KEY (teacher_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES rooms(id),
    FOREIGN KEY (day_id) REFERENCES weekdays(id),
    FOREIGN KEY (time_slot_id) REFERENCES time_slots(id),
    UNIQUE KEY unique_class_day_slot (class_trade_section_id, day_id, time_slot_id, academic_session),
    INDEX idx_teacher_schedule (teacher_id, day_id, time_slot_id, academic_session),
    INDEX idx_room_schedule (room_id, day_id, time_slot_id, academic_session)
);

-- =====================================================================================
-- SECTION 4: ENHANCED EVALUATION AND MARKING SYSTEM
-- =====================================================================================

-- Enhanced student subject marks table with proper grade calculation
CREATE TABLE IF NOT EXISTS enhanced_student_marks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    class_trade_section_id INT NOT NULL,
    subject_id INT NOT NULL,
    exam_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,

    -- Marks components
    theory_marks DECIMAL(6,2) DEFAULT 0.00,
    practical_marks DECIMAL(6,2) DEFAULT 0.00,
    cce_marks DECIMAL(6,2) DEFAULT 0.00,

    -- Calculated fields
    total_marks DECIMAL(6,2) DEFAULT 0.00,
    percentage DECIMAL(5,2) DEFAULT 0.00,
    grade VARCHAR(5),
    grade_points DECIMAL(3,2) DEFAULT 0.00,

    -- Subject categorization for grade calculation
    include_in_grand_total BOOLEAN DEFAULT TRUE,
    subject_category VARCHAR(50),

    -- Status fields
    is_pass BOOLEAN DEFAULT FALSE,
    is_absent BOOLEAN DEFAULT FALSE,
    remarks TEXT,

    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,

    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (class_trade_section_id) REFERENCES class_trade_sections(id),
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    FOREIGN KEY (exam_id) REFERENCES exams(exam_id),
    FOREIGN KEY (created_by) REFERENCES users(id),

    UNIQUE KEY unique_student_subject_exam (student_id, subject_id, exam_id, academic_session),
    INDEX idx_student_session (student_id, academic_session),
    INDEX idx_class_subject_session (class_trade_section_id, subject_id, academic_session)
);

-- Create grade calculation rules table
CREATE TABLE IF NOT EXISTS grade_calculation_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    academic_session VARCHAR(20) NOT NULL,
    min_percentage DECIMAL(5,2) NOT NULL,
    max_percentage DECIMAL(5,2) NOT NULL,
    grade VARCHAR(5) NOT NULL,
    grade_points DECIMAL(3,2) NOT NULL,
    description VARCHAR(100),
    is_passing_grade BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_percentage (academic_session, min_percentage, max_percentage)
);

-- Insert grade calculation rules
INSERT IGNORE INTO grade_calculation_rules (academic_session, min_percentage, max_percentage, grade, grade_points, description, is_passing_grade) VALUES
('2023-2024', 90.00, 100.00, 'A+', 10.00, 'Outstanding', TRUE),
('2023-2024', 80.00, 89.99, 'A', 9.00, 'Excellent', TRUE),
('2023-2024', 70.00, 79.99, 'B+', 8.00, 'Very Good', TRUE),
('2023-2024', 60.00, 69.99, 'B', 7.00, 'Good', TRUE),
('2023-2024', 50.00, 59.99, 'C+', 6.00, 'Above Average', TRUE),
('2023-2024', 40.00, 49.99, 'C', 5.00, 'Average', TRUE),
('2023-2024', 33.00, 39.99, 'D', 4.00, 'Below Average', TRUE),
('2023-2024', 0.00, 32.99, 'F', 0.00, 'Fail', FALSE);

-- =====================================================================================
-- SECTION 5: EXAMINATION PATTERN AND QUESTION PAPER SYSTEM
-- =====================================================================================

-- Create chapters table for syllabus management
CREATE TABLE IF NOT EXISTS subject_chapters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL,
    chapter_number INT NOT NULL,
    chapter_name VARCHAR(200) NOT NULL,
    chapter_description TEXT,
    weightage_percentage DECIMAL(5,2) DEFAULT 0.00,
    total_periods INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    academic_session VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    UNIQUE KEY unique_subject_chapter (subject_id, class_level, chapter_number, academic_session),
    INDEX idx_subject_class_session (subject_id, class_level, academic_session)
);

-- Create question types table
CREATE TABLE IF NOT EXISTS question_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_code VARCHAR(20) NOT NULL UNIQUE,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_marks DECIMAL(4,2) DEFAULT 1.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert standard question types
INSERT IGNORE INTO question_types (type_code, type_name, description, default_marks) VALUES
('MCQ', 'Multiple Choice Questions', 'Single correct answer from 4 options', 1.00),
('SA', 'Short Answer', 'Brief answers in 2-3 lines', 2.00),
('LA', 'Long Answer', 'Detailed answers in 5-7 lines', 5.00),
('VLA', 'Very Long Answer', 'Comprehensive answers', 8.00),
('NUMERICAL', 'Numerical Problems', 'Mathematical calculations', 3.00),
('DIAGRAM', 'Diagram Based', 'Questions requiring diagrams', 4.00),
('PRACTICAL', 'Practical Questions', 'Hands-on assessment', 6.00);

-- Create exam patterns table
CREATE TABLE IF NOT EXISTS exam_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL,
    subject_id INT NOT NULL,
    class_level VARCHAR(10) NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    total_marks DECIMAL(6,2) NOT NULL,
    duration_minutes INT NOT NULL,
    total_questions INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES academic_subjects(id),
    INDEX idx_subject_class_pattern (subject_id, class_level, academic_session)
);

-- Create chapter-wise marks scheme
CREATE TABLE IF NOT EXISTS chapter_marks_scheme (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_pattern_id INT NOT NULL,
    chapter_id INT NOT NULL,
    question_type_id INT NOT NULL,
    number_of_questions INT DEFAULT 1,
    marks_per_question DECIMAL(4,2) DEFAULT 1.00,
    total_marks DECIMAL(6,2) DEFAULT 0.00,
    is_compulsory BOOLEAN DEFAULT TRUE,
    choice_available INT DEFAULT 0, -- Number of questions to choose from if choice is given
    weightage_percentage DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_pattern_id) REFERENCES exam_patterns(id),
    FOREIGN KEY (chapter_id) REFERENCES subject_chapters(id),
    FOREIGN KEY (question_type_id) REFERENCES question_types(id),
    INDEX idx_pattern_chapter (exam_pattern_id, chapter_id)
);

-- Create question paper templates
CREATE TABLE IF NOT EXISTS question_paper_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    exam_pattern_id INT NOT NULL,
    academic_session VARCHAR(20) NOT NULL,
    instructions TEXT,
    time_allocation TEXT,
    marking_scheme TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_pattern_id) REFERENCES exam_patterns(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- =====================================================================================
-- SECTION 6: COMPREHENSIVE VIEWS FOR THE NEW SYSTEM
-- =====================================================================================

-- View 1: Complete Trade-Subject Curriculum
CREATE OR REPLACE VIEW v_trade_curriculum AS
SELECT
    at.trade_code,
    at.trade_name,
    tsc.class_level,
    tsc.academic_session,
    asub.subject_code,
    asub.subject_name,
    asub.subject_category,
    tsc.subject_type,
    tsc.is_mandatory,
    asub.include_in_grand_total,
    asub.theory_weightage,
    asub.practical_weightage,
    asub.cce_weightage,
    asub.total_max_marks,
    CASE
        WHEN tsc.subject_type = 'compulsory_core' THEN 'Core Subject'
        WHEN tsc.subject_type = 'selective_option' THEN 'Choose One'
        WHEN tsc.subject_type = 'compulsory_language' THEN 'Language'
        WHEN tsc.subject_type = 'additional_compulsory' THEN 'Additional (Not in Total)'
        WHEN tsc.subject_type = 'optional' THEN 'Optional (Not in Total)'
        ELSE 'Other'
    END AS subject_type_description
FROM academic_trades at
JOIN trade_subject_combinations tsc ON at.id = tsc.trade_id
JOIN academic_subjects asub ON tsc.subject_id = asub.id
ORDER BY at.trade_code, tsc.class_level, asub.subject_order, asub.subject_name;

-- View 2: Class-Trade-Section with Room Allocations
CREATE OR REPLACE VIEW v_class_allocations AS
SELECT
    cts.id AS class_trade_section_id,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    asec.section_name,
    cts.academic_session,
    cts.total_students,
    r.room_number,
    r.room_name,
    r.capacity,
    ra.allocation_start_date,
    ra.allocation_end_date,
    ra.is_active AS allocation_active,
    CASE
        WHEN ra.id IS NOT NULL THEN 'Allocated'
        ELSE 'Not Allocated'
    END AS allocation_status
FROM class_trade_sections cts
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_sections asec ON cts.section_id = asec.id
LEFT JOIN room_allocations ra ON cts.id = ra.class_trade_section_id AND ra.is_active = TRUE
LEFT JOIN rooms r ON ra.room_id = r.id
ORDER BY cts.academic_session, cts.class_level, at.trade_code, asec.section_code;

-- View 3: Enhanced Student Marks with Proper Grade Calculation
CREATE OR REPLACE VIEW v_enhanced_student_marks AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    s.father_name,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    esm.academic_session,
    asub.subject_code,
    asub.subject_name,
    asub.subject_category,
    e.exam_name,

    -- Marks components
    esm.theory_marks,
    esm.practical_marks,
    esm.cce_marks,
    esm.total_marks,
    asub.total_max_marks AS max_marks,
    esm.percentage,

    -- Grade information
    esm.grade,
    esm.grade_points,
    gcr.description AS grade_description,
    esm.is_pass,

    -- Subject categorization
    esm.include_in_grand_total,
    esm.subject_category AS marks_category,
    asub.include_in_grand_total AS subject_include_in_total,

    -- Additional info
    esm.is_absent,
    esm.remarks,
    esm.created_at AS marks_entry_date

FROM enhanced_student_marks esm
JOIN students s ON esm.student_id = s.id
JOIN class_trade_sections cts ON esm.class_trade_section_id = cts.id
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_sections asec ON cts.section_id = asec.id
JOIN academic_subjects asub ON esm.subject_id = asub.id
JOIN exams e ON esm.exam_id = e.exam_id
LEFT JOIN grade_calculation_rules gcr ON esm.academic_session = gcr.academic_session
    AND esm.percentage BETWEEN gcr.min_percentage AND gcr.max_percentage
ORDER BY s.student_id, esm.academic_session, asub.subject_order, e.exam_name;

-- View 4: Student Grand Total Calculation (Only subjects that count)
CREATE OR REPLACE VIEW v_student_grand_totals AS
SELECT
    s.student_id AS roll_number,
    s.name AS student_name,
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    esm.academic_session,
    e.exam_name,

    -- Grand total calculation (only subjects that count)
    COUNT(CASE WHEN asub.include_in_grand_total = TRUE THEN 1 END) AS subjects_in_total,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN esm.total_marks ELSE 0 END) AS grand_total_marks,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN asub.total_max_marks ELSE 0 END) AS grand_max_marks,
    ROUND((SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN esm.total_marks ELSE 0 END) /
           SUM(CASE WHEN asub.include_in_grand_total = TRUE THEN asub.total_max_marks ELSE 0 END)) * 100, 2) AS grand_percentage,

    -- Additional subjects (not counted in total)
    COUNT(CASE WHEN asub.include_in_grand_total = FALSE THEN 1 END) AS additional_subjects,
    SUM(CASE WHEN asub.include_in_grand_total = FALSE THEN esm.total_marks ELSE 0 END) AS additional_total_marks,
    SUM(CASE WHEN asub.include_in_grand_total = FALSE THEN asub.total_max_marks ELSE 0 END) AS additional_max_marks,

    -- Pass/Fail status
    SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.is_pass = FALSE THEN 1 ELSE 0 END) AS failed_core_subjects,
    CASE
        WHEN SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.is_pass = FALSE THEN 1 ELSE 0 END) = 0 THEN 'PASS'
        ELSE 'FAIL'
    END AS overall_result

FROM enhanced_student_marks esm
JOIN students s ON esm.student_id = s.id
JOIN class_trade_sections cts ON esm.class_trade_section_id = cts.id
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_sections asec ON cts.section_id = asec.id
JOIN academic_subjects asub ON esm.subject_id = asub.id
JOIN exams e ON esm.exam_id = e.exam_id
GROUP BY s.id, cts.id, esm.academic_session, e.exam_id
ORDER BY s.student_id, esm.academic_session, e.exam_name;

-- View 5: Timetable View
CREATE OR REPLACE VIEW v_class_timetables AS
SELECT
    cts.class_level,
    at.trade_code,
    at.trade_name,
    asec.section_code,
    ct.academic_session,
    wd.day_name,
    wd.day_short,
    ts.slot_name,
    ts.start_time,
    ts.end_time,
    asub.subject_code,
    asub.subject_name,
    u.name AS teacher_name,
    r.room_number,
    r.room_name,
    ct.is_active
FROM class_timetables ct
JOIN class_trade_sections cts ON ct.class_trade_section_id = cts.id
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_sections asec ON cts.section_id = asec.id
JOIN academic_subjects asub ON ct.subject_id = asub.id
JOIN weekdays wd ON ct.day_id = wd.id
JOIN time_slots ts ON ct.time_slot_id = ts.id
LEFT JOIN users u ON ct.teacher_id = u.id
LEFT JOIN rooms r ON ct.room_id = r.id
WHERE ct.is_active = TRUE
ORDER BY cts.class_level, at.trade_code, asec.section_code, wd.day_order, ts.slot_order;

-- View 6: Trade Performance Analysis (Enhanced)
CREATE OR REPLACE VIEW v_enhanced_trade_performance AS
SELECT
    at.trade_code,
    at.trade_name,
    cts.class_level,
    esm.academic_session,

    -- Student statistics
    COUNT(DISTINCT s.id) AS total_students,
    COUNT(DISTINCT esm.subject_id) AS total_subjects,

    -- Performance metrics (only subjects that count in grand total)
    ROUND(AVG(CASE WHEN asub.include_in_grand_total = TRUE THEN esm.percentage END), 2) AS avg_percentage_core_subjects,
    MAX(CASE WHEN asub.include_in_grand_total = TRUE THEN esm.percentage END) AS highest_percentage,
    MIN(CASE WHEN asub.include_in_grand_total = TRUE THEN esm.percentage END) AS lowest_percentage,

    -- Pass rates
    ROUND((SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.is_pass = TRUE THEN 1 ELSE 0 END) * 100.0 /
           COUNT(CASE WHEN asub.include_in_grand_total = TRUE THEN 1 END)), 2) AS core_subjects_pass_rate,

    -- Grade distribution (core subjects only)
    SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.grade IN ('A+', 'A') THEN 1 ELSE 0 END) AS excellent_grades,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.grade IN ('B+', 'B') THEN 1 ELSE 0 END) AS good_grades,
    SUM(CASE WHEN asub.include_in_grand_total = TRUE AND esm.grade = 'F' THEN 1 ELSE 0 END) AS failed_grades

FROM enhanced_student_marks esm
JOIN students s ON esm.student_id = s.id
JOIN class_trade_sections cts ON esm.class_trade_section_id = cts.id
JOIN academic_trades at ON cts.trade_id = at.id
JOIN academic_subjects asub ON esm.subject_id = asub.id
GROUP BY at.id, cts.class_level, esm.academic_session
ORDER BY esm.academic_session, cts.class_level, at.trade_code;
