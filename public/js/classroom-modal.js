/**
 * Classroom Modal with Multi-Tab Support
 * Handles classroom details display with tabs for Overview, IT Equipment, Electrical, and Students
 */

$(document).ready(function() {
    console.log('🏫 Classroom Modal initialized');

    // Tab switching functionality
    $(document).on('click', '.classroom-tab', function() {
        const tabId = $(this).attr('id');
        const contentId = tabId.replace('tab-', 'content-');
        
        // Remove active class from all tabs and content
        $('.classroom-tab').removeClass('active').removeClass('border-blue-500').removeClass('text-blue-600')
                          .addClass('border-transparent').addClass('text-gray-500');
        $('.tab-content').removeClass('active').addClass('hidden');
        
        // Add active class to clicked tab and corresponding content
        $(this).addClass('active').addClass('border-blue-500').addClass('text-blue-600')
               .removeClass('border-transparent').removeClass('text-gray-500');
        $('#' + contentId).addClass('active').removeClass('hidden');
        
        console.log('🔄 Switched to tab:', contentId);
    });

    // Function to populate tabs with classroom data
    window.populateTabsWithData = function(classroomData) {
        const { classroom, students, equipment } = classroomData;
        
        console.log('📊 Populating tabs with data:', classroomData);

        // Overview Tab
        const overviewHTML = generateOverviewHTML(classroom, students);
        $('#content-overview').html(overviewHTML);

        // IT Equipment Tab
        const itEquipmentHTML = generateITEquipmentHTML(equipment.it || []);
        $('#content-equipment').html(itEquipmentHTML);

        // Electrical Equipment Tab
        const electricalHTML = generateElectricalHTML(equipment.electrical || []);
        $('#content-electrical').html(electricalHTML);

        // Students Tab
        const studentsHTML = generateStudentsHTML(students, classroom);
        $('#content-students').html(studentsHTML);
    };

    // Generate Overview Tab HTML
    function generateOverviewHTML(classroom, students) {
        return `
            <div class="space-y-6">
                <!-- Classroom Info Card -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-door-open text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-800">${classroom.room_number || 'Unknown Room'}</h3>
                            <p class="text-gray-600">${classroom.room_name || 'Classroom'}</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-3 bg-white rounded-lg border">
                            <div class="text-2xl font-bold text-blue-600">${classroom.capacity || 50}</div>
                            <div class="text-sm text-gray-500">Capacity</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg border">
                            <div class="text-2xl font-bold text-green-600">${students.total || 0}</div>
                            <div class="text-sm text-gray-500">Students</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg border">
                            <div class="text-2xl font-bold text-purple-600">${classroom.floor || 'N/A'}</div>
                            <div class="text-sm text-gray-500">Floor</div>
                        </div>
                        <div class="text-center p-3 bg-white rounded-lg border">
                            <div class="text-2xl font-bold text-orange-600">${classroom.utilization_percentage || 0}%</div>
                            <div class="text-sm text-gray-500">Utilization</div>
                        </div>
                    </div>
                </div>

                <!-- Class Information -->
                ${classroom.grade ? `
                <div class="bg-white rounded-lg p-6 border border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Class Information</h4>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500">Grade</label>
                            <div class="text-lg font-semibold text-gray-800">${classroom.grade}</div>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Section</label>
                            <div class="text-lg font-semibold text-gray-800">${classroom.section || 'N/A'}</div>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Trade</label>
                            <div class="text-lg font-semibold text-gray-800">${classroom.trade_name || 'N/A'}</div>
                        </div>
                        ${classroom.incharge_name ? `
                        <div class="md:col-span-3">
                            <label class="text-sm font-medium text-gray-500">Class Teacher</label>
                            <div class="text-lg font-semibold text-gray-800">${classroom.incharge_name}</div>
                        </div>
                        ` : ''}
                    </div>
                </div>
                ` : `
                <div class="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-yellow-600 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-lg font-semibold text-yellow-800">Unassigned Classroom</h4>
                            <p class="text-yellow-700">This classroom is currently not assigned to any class.</p>
                        </div>
                    </div>
                </div>
                `}
            </div>
        `;
    }

    // Generate IT Equipment Tab HTML
    function generateITEquipmentHTML(equipment) {
        if (!equipment || equipment.length === 0) {
            return `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-laptop text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No IT Equipment</h3>
                    <p class="text-gray-500">No IT devices are currently assigned to this classroom.</p>
                </div>
            `;
        }

        const equipmentCards = equipment.map(item => {
            const statusColor = item.status === 'working' ? 'green' : 
                              item.status === 'faulty' ? 'red' : 'yellow';
            const statusBg = item.status === 'working' ? 'bg-green-100 text-green-800' : 
                           item.status === 'faulty' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800';

            return `
                <div class="equipment-card bg-white rounded-lg border border-gray-200 p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-laptop text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">${item.item_name || 'IT Equipment'}</h4>
                                <p class="text-sm text-gray-500">${item.item_type || 'Device'}</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusBg}">
                            ${item.status || 'Unknown'}
                        </span>
                    </div>
                    
                    <div class="space-y-2 text-sm">
                        ${item.serial_number ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Serial:</span>
                            <span class="font-mono text-gray-800">${item.serial_number}</span>
                        </div>
                        ` : ''}
                        ${item.model ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Model:</span>
                            <span class="text-gray-800">${item.model}</span>
                        </div>
                        ` : ''}
                        ${item.purchase_date ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Purchase:</span>
                            <span class="text-gray-800">${new Date(item.purchase_date).toLocaleDateString()}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">IT Equipment (${equipment.length} items)</h3>
                    <div class="text-sm text-gray-500">
                        Working: ${equipment.filter(item => item.status === 'working').length} | 
                        Faulty: ${equipment.filter(item => item.status === 'faulty').length}
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    ${equipmentCards}
                </div>
            </div>
        `;
    }

    // Generate Electrical Equipment Tab HTML
    function generateElectricalHTML(equipment) {
        if (!equipment || equipment.length === 0) {
            return `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bolt text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No Electrical Equipment</h3>
                    <p class="text-gray-500">No electrical fixtures are currently assigned to this classroom.</p>
                </div>
            `;
        }

        const equipmentCards = equipment.map(item => {
            const statusColor = item.status === 'working' ? 'green' : 
                              item.status === 'faulty' ? 'red' : 'yellow';
            const statusBg = item.status === 'working' ? 'bg-green-100 text-green-800' : 
                           item.status === 'faulty' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800';

            return `
                <div class="equipment-card bg-white rounded-lg border border-gray-200 p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-bolt text-yellow-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">${item.item_name || 'Electrical Equipment'}</h4>
                                <p class="text-sm text-gray-500">${item.item_type || 'Electrical'}</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusBg}">
                            ${item.status || 'Unknown'}
                        </span>
                    </div>
                    
                    <div class="space-y-2 text-sm">
                        ${item.serial_number ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Serial:</span>
                            <span class="font-mono text-gray-800">${item.serial_number}</span>
                        </div>
                        ` : ''}
                        ${item.wattage ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Power:</span>
                            <span class="text-gray-800">${item.wattage}W</span>
                        </div>
                        ` : ''}
                        ${item.installation_date ? `
                        <div class="flex justify-between">
                            <span class="text-gray-500">Installed:</span>
                            <span class="text-gray-800">${new Date(item.installation_date).toLocaleDateString()}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">Electrical Equipment (${equipment.length} items)</h3>
                    <div class="text-sm text-gray-500">
                        Working: ${equipment.filter(item => item.status === 'working').length} | 
                        Faulty: ${equipment.filter(item => item.status === 'faulty').length}
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    ${equipmentCards}
                </div>
            </div>
        `;
    }

    // Generate Students Tab HTML
    function generateStudentsHTML(students, classroom) {
        return `
            <div class="space-y-6">
                <!-- Student Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-users text-white"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-blue-600">${students.total || 0}</div>
                                <div class="text-sm text-gray-600">Total Students</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 rounded-lg p-6 border border-green-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-male text-white"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-green-600">${students.boys || 0}</div>
                                <div class="text-sm text-gray-600">Boys</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-pink-50 rounded-lg p-6 border border-pink-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-female text-white"></i>
                            </div>
                            <div>
                                <div class="text-2xl font-bold text-pink-600">${students.girls || 0}</div>
                                <div class="text-sm text-gray-600">Girls</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Utilization Chart -->
                <div class="bg-white rounded-lg p-6 border border-gray-200">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">Classroom Utilization</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <span>Occupied</span>
                            <span>${students.total || 0}/${classroom.capacity || 50} (${classroom.utilization_percentage || 0}%)</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-600 h-3 rounded-full transition-all duration-500" 
                                 style="width: ${classroom.utilization_percentage || 0}%"></div>
                        </div>
                    </div>
                </div>

                ${students.total === 0 ? `
                <div class="bg-gray-50 rounded-lg p-8 text-center border border-gray-200">
                    <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-slash text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">No Students Assigned</h3>
                    <p class="text-gray-500">This classroom currently has no students assigned to it.</p>
                </div>
                ` : ''}
            </div>
        `;
    }

    // Close modal function
    window.closeModal = function() {
        const $modal = $('#classroomModal');
        $modal.addClass('hidden').css('display', 'none');
        $('body').css('overflow', 'auto');
    };

    console.log('✅ Classroom Modal functions loaded');
});
