/**
 * Infrastructure Page JavaScript
 */

console.log('🚀 Infrastructure script starting...');
console.log('🚀 jQuery available:', typeof $);

$(document).ready(function() {
    console.log('🎯 Infrastructure Command Center initialized with jQuery');
    
    // 1. First, append the modal HTML to the body immediately
    $('body').append(modalHTML);
    console.log('Modal appended to body');
    
    // Tab click handling - simplified 4-line approach
    $(document).on('click', '#modalTabs .tab-btn', function(e) {
        e.preventDefault();
        const tabName = $(this).data('tab');
        console.log('Tab clicked:', tabName);
        
        // 1. Hide every .tab-content pane
        $('.tab-content').hide();
        
        // 2. Show exactly the one whose id is in your map
        const tabMap = {
            'equipment': 'equipmentTab',
            'electrical': 'electricalTab', 
            'students': 'studentsTab',
            'itEquipment': 'equipmentTab',
            'electricalEquipment': 'electricalTab',
            'overview': 'overviewTab'  // Adding overview tab mapping
        };
        const tabId = tabMap[tabName] || tabName + 'Tab';
        
        // Show the selected tab only
        $(`#${tabId}`).show();
        
        // 3. Remove the "active" class from all buttons
        $('#modalTabs .tab-btn').removeClass('active border-blue-600 text-blue-900')
                               .addClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
        
        // 4. Add "active" to the one just clicked
        $(this).removeClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300')
               .addClass('active border-blue-600 text-blue-900');
               
        console.log('Tab activation complete:', {
            tabId,
            isVisible: $(`#${tabId}`).is(':visible'),
            display: $(`#${tabId}`).css('display'),
            activeButton: $(this).data('tab')
        });
    });
    
    // Show classroom details function
    async function showClassroomDetails(roomId) {
        const $modal = $('#classroomModal');
        const $modalLoading = $('#modalLoading');
        const $modalContent = $('#modalContent');
        const $modalTitle = $('#modalTitle');
        const $modalSubtitle = $('#modalSubtitle');
        
        try {
            // Make sure roomId is a number and log more details
            const parsedRoomId = parseInt(roomId, 10);
            if (isNaN(parsedRoomId)) {
                console.error('Invalid room ID:', roomId);
                return;
            }
            
            console.log('📡 Fetching data for room:', parsedRoomId, '(type: ' + typeof parsedRoomId + ')');
            
            // Show modal and loading state
            $modalLoading.show();
            $('.tab-content').hide(); // Use consistent hide() method
            $modal.removeClass('hidden');

            // Construct API URL with the parsed room ID
            const apiUrl = `/principal/api/classroom/${parsedRoomId}`;
            console.log('🔗 API URL:', apiUrl);

            // Fetch classroom data with explicit numeric room ID
            const response = await fetch(apiUrl);
            
            // Log response details for debugging
            console.log('📡 Response status:', response.status, response.statusText);
            
            const result = await response.json();

            console.log('📊 API Response for room', parsedRoomId, ':', result);
            console.log('📊 API Raw Data:', JSON.stringify(result).substring(0, 500) + '...');

            if (result.success && result.data) {
                // Fix: The API structure has changed - equipment is in result.data.equipment
                const classroom = result.data.classroom;
                
                // Get equipment data from the correct location in API response
                let itEquipment = [];
                let electricalEquipment = [];
                
                // Process equipment data correctly based on categories
                if (result.data.equipment) {
                    // Get IT equipment
                    itEquipment = result.data.equipment.it || [];
                    
                    // Get electrical equipment 
                    electricalEquipment = result.data.equipment.electrical || [];
                    
                    // Log what we received from API for debugging
                    console.log('Equipment from API:', {
                        it: itEquipment.map(i => i.item_name || i.name),
                        electrical: electricalEquipment.map(i => i.item_name || i.name)
                    });
                }
                
                console.log('Equipment categorized:', {
                    itEquipment: itEquipment.length,
                    electricalEquipment: electricalEquipment.length
                });
                
                // Update modal header with classroom info
                $modalTitle.text(classroom.name || classroom.room_name || 'Classroom Details');
                $modalSubtitle.text(`${classroom.room_number || ''} ${classroom.floor ? '- Floor ' + classroom.floor : ''}`);
                
                // Update classroom general info section
                $('#classroomName').text(classroom.room_name || classroom.room_number || 'Classroom');
                $('#classroomDetails').text(`Floor ${classroom.floor || '0'} • Capacity: ${classroom.capacity || 50} students`);
                
                // Show class details if available
                if (classroom.grade && classroom.section) {
                    $('#classroomAssignment').text(`Class ${classroom.grade} ${classroom.section || ''} ${classroom.trade_name ? '• ' + classroom.trade_name : ''} ${classroom.incharge_name ? '• In-charge: ' + classroom.incharge_name : ''}`);
                } else {
                    $('#classroomAssignment').text('Unassigned Classroom');
                }
                
                // Update occupancy info
                const studentCount = classroom.student_count || 0;
                const capacity = classroom.capacity || 50;
                $('#classroomOccupancy').text(`${studentCount}/${capacity} Occupied`);
                
                // Update status badge (change appearance based on utilization)
                const utilizationPercentage = Math.round((studentCount / capacity) * 100);
                let statusClass = 'bg-green-100 text-green-800';
                let statusText = 'Operational';
                
                if (utilizationPercentage > 90) {
                    statusClass = 'bg-red-100 text-red-800';
                    statusText = 'Full';
                } else if (utilizationPercentage > 75) {
                    statusClass = 'bg-yellow-100 text-yellow-800';
                    statusText = 'High Occupancy';
                }
                
                $('#classroomStatusBadge').attr('class', `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}`);
                $('#classroomStatusBadge').text(statusText);
                
                // Generate overview content with general classroom information
                let overviewHTML = generateOverviewHTML(classroom);
                $('#modalOverviewContent').html(overviewHTML);
                
                // Generate HTML for tabs with properly categorized equipment
                console.log('Generating tab content with:', {
                    itEquipment: itEquipment.length, 
                    electricalEquipment: electricalEquipment.length
                });
                
                const itHTML = generateEquipmentHTML(itEquipment);
                const electricalHTML = generateElectricalHTML(electricalEquipment);
                
                console.log('Generated HTML lengths:', {
                    itHTML: itHTML.length, 
                    electricalHTML: electricalHTML.length
                });
                
                // Set content to tabs
                $('#modalEquipmentContent').html(itHTML);
                $('#modalElectricalContent').html(electricalHTML);
                
                // Ensure tab content containers are found
                console.log('Tab containers found:', {
                    overviewTab: $('#overviewTab').length,
                    equipmentTab: $('#equipmentTab').length,
                    electricalTab: $('#electricalTab').length,
                    modalOverviewContent: $('#modalOverviewContent').length,
                    modalEquipmentContent: $('#modalEquipmentContent').length,
                    modalElectricalContent: $('#modalElectricalContent').length
                });
                
                // Show the first tab by default and ensure it's visible
                $('.tab-content').hide();
                $('#overviewTab').show(); // Default to overview tab
                
                // Make sure any hidden classes are removed as well
                $('.tab-content').removeClass('hidden');
                
                // Activate first tab button
                $('#modalTabs .tab-btn').removeClass('active border-blue-600 text-blue-900')
                    .addClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
                
                $('#modalTabs .tab-btn[data-tab="overview"]').addClass('active border-blue-600 text-blue-900')
                    .removeClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
                
                $modalLoading.hide();
            } else {
                console.error('API returned error or invalid data:', result);
                $modalLoading.hide();
                
                $('#modalEquipmentContent').html(`
                    <div class="p-6 text-center text-red-500">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <h3 class="text-lg font-semibold mb-2">Error Loading Data</h3>
                        <p class="text-sm">Failed to load classroom details. Please try again.</p>
                        <pre class="mt-4 text-xs text-left bg-gray-100 p-2 rounded overflow-auto max-h-40">${
                            JSON.stringify(result, null, 2)
                        }</pre>
                    </div>
                `);
                
                // Ensure first tab is still visible
                $('.tab-content').hide().removeClass('hidden');
                $('#overviewTab').show();
            }
        } catch (error) {
            console.error('💥 Error in showClassroomDetails:', error);
            
            // Show error in modal tabs
            $('#modalEquipmentContent').html(`
                <div class="p-6 text-center text-red-500">
                    <i class="fas fa-exclamation-circle text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">Connection Error</h3>
                    <p class="text-sm">Failed to connect to server. Please try again.</p>
                    <div class="mt-4 text-xs text-left bg-gray-100 p-2 rounded overflow-auto max-h-40">
                        ${error.toString()}
                    </div>
                </div>
            `);
            
            // Ensure first tab is still visible
            $('.tab-content').hide().removeClass('hidden');
            $('#overviewTab').show();
            
            // Hide loading
            $modalLoading.hide();
        }
    }

    // HTML generator for classroom overview
    function generateOverviewHTML(classroom) {
        if (!classroom) {
            return `
                <div class="p-6 text-center text-gray-500">
                    <i class="fas fa-school text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">No Classroom Data</h3>
                    <p class="text-sm">Classroom information could not be loaded.</p>
                </div>
            `;
        }

        // Generate class-specific information if available
        let classInfoHTML = '';
        if (classroom.grade || classroom.section || classroom.trade_name) {
            classInfoHTML = `
                <div class="bg-indigo-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-indigo-800 mb-2">Class Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <p><strong>Grade:</strong> ${classroom.grade || 'N/A'}</p>
                            <p><strong>Section:</strong> ${classroom.section || 'N/A'}</p>
                            <p><strong>Trade/Stream:</strong> ${classroom.trade_name || 'N/A'}</p>
                        </div>
                        <div class="space-y-2">
                            <p><strong>Class Teacher:</strong> ${classroom.incharge_name || 'Not Assigned'}</p>
                            <p><strong>Contact:</strong> ${classroom.incharge_email || 'N/A'}</p>
                            <p><strong>Session:</strong> ${classroom.session || '2023-2024'}</p>
                        </div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="p-6">
                <div class="bg-blue-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Room Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <p><strong>Room Name:</strong> ${classroom.room_name || 'N/A'}</p>
                            <p><strong>Room Number:</strong> ${classroom.room_number || 'N/A'}</p>
                            <p><strong>Floor:</strong> ${classroom.floor || 'Ground'}</p>
                            <p><strong>Capacity:</strong> ${classroom.capacity || 'Unknown'} students</p>
                        </div>
                        <div class="space-y-2">
                            <p><strong>Building:</strong> ${classroom.building || 'Main Building'}</p>
                            <p><strong>Room Type:</strong> ${classroom.grade ? 'Classroom' : 'Multi-purpose Room'}</p>
                            <p><strong>Students:</strong> ${classroom.student_count || 0} / ${classroom.capacity || 50} 
                                (${Math.round(((classroom.student_count || 0) / (classroom.capacity || 50)) * 100)}% occupied)</p>
                            <p><strong>Facilities:</strong> ${classroom.facilities || 'Standard'}</p>
                        </div>
                    </div>
                </div>

                ${classInfoHTML}

                ${classroom.description ? `
                <div class="mb-6">
                    <h4 class="font-semibold mb-2">Description</h4>
                    <p class="text-gray-700">${classroom.description}</p>
                </div>` : ''}

                ${classroom.notes ? `
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">Notes</h4>
                    <p class="text-gray-700">${classroom.notes}</p>
                </div>` : ''}
            </div>
        `;
    }

    // HTML generator for IT equipment
    function generateEquipmentHTML(itEquipment) {
        if (!itEquipment || !Array.isArray(itEquipment) || itEquipment.length === 0) {
            return `
                <div class="p-6 text-center text-gray-500">
                    <i class="fas fa-desktop text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">No IT Equipment</h3>
                    <p class="text-sm">No IT devices assigned to this classroom.</p>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-6">
                ${itEquipment.map(item => {
                    return `
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 equipment-card">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex items-center">
                                    <span class="text-xl mr-2">💻</span>
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">${item.item_name || 'Unknown Device'}</h4>
                                        <p class="text-xs text-gray-600 capitalize">${item.item_type || 'Equipment'}</p>
                                    </div>
                                </div>
                                <span class="px-2 py-1 rounded-full text-xs font-semibold uppercase ${getStatusClass(item.status)}">
                                    ${item.status || 'Unknown'}
                                </span>
                            </div>
                            <div class="grid grid-cols-2 gap-2 text-sm text-gray-700">
                                <div><strong>Serial:</strong> <span class="font-mono">${item.serial_number || 'N/A'}</span></div>
                                ${item.manufacturer ? `<div><strong>Brand:</strong> ${item.manufacturer}</div>` : ''}
                                ${item.model ? `<div><strong>Model:</strong> ${item.model}</div>` : ''}
                                ${item.purchase_date ? `<div><strong>Purchased:</strong> ${new Date(item.purchase_date).toLocaleDateString()}</div>` : ''}
                                ${item.warranty_expiry ? `<div><strong>Warranty:</strong> ${new Date(item.warranty_expiry).toLocaleDateString()}</div>` : ''}
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    // Updated HTML generator for electrical equipment
    function generateElectricalHTML(electricalEquipment) {
        if (!electricalEquipment || !Array.isArray(electricalEquipment) || electricalEquipment.length === 0) {
            return `
                <div class="p-6 text-center text-gray-500">
                    <i class="fas fa-plug text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">No Electrical Equipment</h3>
                    <p class="text-sm">No electrical devices assigned to this classroom.</p>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-6">
                ${electricalEquipment.map(item => {
                    // Determine if item is a UPS for proper icon and type display
                    const isUPS = item.item_name && item.item_name.toLowerCase().includes('ups');
                    const itemIcon = isUPS ? 'fa-battery-full' : 'fa-plug';
                    const itemType = isUPS ? 'UPS' : (item.item_type || 'Electrical Equipment');
                    
                    return `
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 equipment-card">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex items-center">
                                    <span class="text-xl mr-2">${isUPS ? '🔋' : '⚡'}</span>
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">${item.item_name || 'Unknown Device'}</h4>
                                        <p class="text-xs text-gray-600 capitalize">${itemType}</p>
                                    </div>
                                </div>
                                <span class="px-2 py-1 rounded-full text-xs font-semibold uppercase ${getStatusClass(item.status)}">
                                    ${item.status || 'Unknown'}
                                </span>
                            </div>
                            <div class="grid grid-cols-2 gap-2 text-sm text-gray-700">
                                <div><strong>ID:</strong> <span class="font-mono">${item.item_id || item.id || 'N/A'}</span></div>
                                ${item.wattage ? `<div><strong>Wattage:</strong> ${item.wattage}W</div>` : ''}
                                ${item.serial_number ? `<div><strong>Serial:</strong> <span class="font-mono">${item.serial_number}</span></div>` : ''}
                                ${item.manufacturer ? `<div><strong>Brand:</strong> ${item.manufacturer}</div>` : ''}
                                ${item.model ? `<div><strong>Model:</strong> ${item.model}</div>` : ''}
                                ${item.installation_date ? `<div><strong>Installed:</strong> ${new Date(item.installation_date).toLocaleDateString()}</div>` : ''}
                                ${item.purchase_date ? `<div><strong>Purchased:</strong> ${new Date(item.purchase_date).toLocaleDateString()}</div>` : ''}
                                ${item.last_maintenance_date ? `<div><strong>Last Maintenance:</strong> ${new Date(item.last_maintenance_date).toLocaleDateString()}</div>` : ''}
                                ${item.warranty_expiry ? `<div><strong>Warranty:</strong> ${new Date(item.warranty_expiry).toLocaleDateString()}</div>` : ''}
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    // Helper function for status colors
    function getStatusClass(status) {
        switch(status?.toLowerCase()) {
            case 'working':
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'maintenance':
            case 'repair':
                return 'bg-yellow-100 text-yellow-800';
            case 'broken':
            case 'inactive':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    // Classroom card click handler
    $(document).on('click', '.classroom-card', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Get the room ID from data attribute and ensure it's a number
        const roomId = parseInt($(this).data('room'), 10);
        
        // Verify we have a valid room ID before proceeding
        if (isNaN(roomId)) {
            console.error('Invalid room ID:', $(this).data('room'));
            return;
        }
        
        console.log('🎯 Classroom ' + roomId + ' clicked (value type: ' + typeof roomId + ')');
        
        // Check modal DOM structure before showing
        console.log('Modal structure check:', {
            modalExists: $('#classroomModal').length,
            equipmentTabExists: $('#equipmentTab').length,
            electricalTabExists: $('#electricalTab').length,
            tabButtons: $('#modalTabs .tab-btn').length
        });
        
        // Pass the numeric room ID to the details function
        showClassroomDetails(roomId);
    });

    // Close modal
    $(document).on('click', '#closeModalBtn', function() {
        $('#classroomModal').addClass('hidden');
    });

    // Close when clicking outside modal content
    $(document).on('click', '#classroomModal', function(e) {
        if ($(e.target).is('#classroomModal')) {
            $('#classroomModal').addClass('hidden');
        }
    });
});

function refreshPageData() {
    console.log('Refreshing infrastructure data...');
}

// Modal HTML structure
const modalHTML = `
<div id="classroomModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b">
                <div>
                    <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Classroom Details</h3>
                    <p id="modalSubtitle" class="text-sm text-gray-600">Loading...</p>
                </div>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Loading State -->
            <div id="modalLoading" class="p-8 text-center">
                <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                <p class="text-gray-600">Loading classroom details...</p>
            </div>

            <!-- Modal Content -->
            <div id="modalContent">
                <!-- Tabs -->
                <div id="modalTabs" class="border-b border-gray-200">
                    <nav class="flex">
                        <button class="tab-btn px-6 py-3 text-sm font-medium border-b-2 border-blue-600 text-blue-900" data-tab="overview">
                            <i class="fas fa-info-circle mr-2"></i>Overview
                        </button>
                        <button class="tab-btn px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="itEquipment">
                            <i class="fas fa-desktop mr-2"></i>IT Equipment
                        </button>
                        <button class="tab-btn px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="electricalEquipment">
                            <i class="fas fa-plug mr-2"></i>Electrical Equipment
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="max-h-96 overflow-y-auto">
                    <div id="overviewTab" class="tab-content">
                        <div id="modalOverviewContent" class="overview-content">
                            <!-- Overview content will be loaded here -->
                        </div>
                    </div>
                    <div id="equipmentTab" class="tab-content">
                        <div id="modalEquipmentContent" class="equipment-content">
                            <!-- IT Equipment content will be loaded here -->
                        </div>
                    </div>
                    <div id="electricalTab" class="tab-content">
                        <div id="modalElectricalContent" class="electrical-content">
                            <!-- Electrical Equipment content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
`;

// Modal HTML is appended in document.ready - don't append it twice

