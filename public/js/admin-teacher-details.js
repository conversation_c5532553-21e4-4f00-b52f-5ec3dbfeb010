/**
 * Admin Teacher Details JavaScript
 * Handles teacher management functionality for admin users
 */

$(document).ready(function() {
    console.log('🎯 Admin Teacher Details page loaded');

    // Global variables
    let currentTeacherData = null;

    // Initialize page functionality
    initializeEventHandlers();
    initializeSearch();
    initializeFilters();

    /**
     * Initialize all event handlers
     */
    function initializeEventHandlers() {
        // View teacher details button
        $(document).on('click', '.viewTeacherBtn', function() {
            const teacherId = $(this).data('teacher-id');
            console.log('👁️ View teacher clicked for ID:', teacherId);
            openTeacherModal(teacherId);
        });

        // Generate CV button
        $(document).on('click', '.generateCVBtn', function() {
            const teacherId = $(this).data('teacher-id');
            const teacherName = $(this).data('teacher-name');
            console.log('📄 Generate CV clicked for:', teacherName);
            generateTeacherCV(teacherId, teacherName, $(this));
        });

        // Modal close button
        $(document).on('click', '#closeModalBtn', function() {
            closeTeacherModal();
        });

        // Modal download CV button
        $(document).on('click', '#downloadCVBtn', function() {
            if (currentTeacherData) {
                generateTeacherCV(currentTeacherData.id, currentTeacherData.name, $(this));
            }
        });

        // Close modal when clicking outside
        $(document).on('click', '#teacherModal', function(e) {
            if (e.target === this) {
                closeTeacherModal();
            }
        });

        // Tab switching
        $(document).on('click', '.tab-btn', function() {
            const tabName = $(this).data('tab');
            switchTab(tabName);
        });

        // Refresh data button
        $('#refreshData').on('click', function() {
            location.reload();
        });

        // Export data button
        $('#exportData').on('click', function() {
            openExportModal();
        });

        // Select all teachers checkbox
        $('#selectAllTeachers').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.teacher-checkbox').prop('checked', isChecked);
            updateSelectionInfo();
        });

        // Individual teacher checkboxes
        $(document).on('change', '.teacher-checkbox', function() {
            updateSelectionInfo();
            
            // Update select all checkbox state
            const totalCheckboxes = $('.teacher-checkbox').length;
            const checkedCheckboxes = $('.teacher-checkbox:checked').length;
            
            if (checkedCheckboxes === 0) {
                $('#selectAllTeachers').prop('indeterminate', false).prop('checked', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                $('#selectAllTeachers').prop('indeterminate', false).prop('checked', true);
            } else {
                $('#selectAllTeachers').prop('indeterminate', true);
            }
        });

        // Export selected button
        $('#exportSelectedBtn').on('click', function() {
            openExportModal();
        });

        // Clear selection button
        $('#clearSelectionBtn').on('click', function() {
            $('.teacher-checkbox, #selectAllTeachers').prop('checked', false);
            updateSelectionInfo();
        });
    }

    /**
     * Initialize search functionality
     */
    function initializeSearch() {
        $('#searchInput').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterTeachers();
        });
    }

    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        $('#departmentFilter, #statusFilter').on('change', function() {
            filterTeachers();
        });
    }

    /**
     * Filter teachers based on search and filter criteria
     */
    function filterTeachers() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const departmentFilter = $('#departmentFilter').val();
        const statusFilter = $('#statusFilter').val();

        $('.teacher-row').each(function() {
            const $row = $(this);
            const name = $row.data('name') || '';
            const email = $row.data('email') || '';
            const department = $row.data('department') || '';
            const status = $row.data('status') || '';

            let showRow = true;

            // Search filter
            if (searchTerm) {
                const searchMatch = name.includes(searchTerm) || 
                                  email.includes(searchTerm) || 
                                  department.toLowerCase().includes(searchTerm);
                if (!searchMatch) showRow = false;
            }

            // Department filter
            if (departmentFilter && department !== departmentFilter) {
                showRow = false;
            }

            // Status filter
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }

            $row.toggle(showRow);
        });
    }

    /**
     * Update selection information display
     */
    function updateSelectionInfo() {
        const selectedCount = $('.teacher-checkbox:checked').length;
        
        if (selectedCount > 0) {
            $('#selectedCount').text(selectedCount);
            $('#selectedTeachersInfo, #exportSelectedBtn, #clearSelectionBtn').removeClass('hidden');
        } else {
            $('#selectedTeachersInfo, #exportSelectedBtn, #clearSelectionBtn').addClass('hidden');
        }
    }

    /**
     * Open teacher details modal
     */
    function openTeacherModal(teacherId) {
        console.log('🔍 Opening teacher modal for ID:', teacherId);
        
        // Show modal
        $('#teacherModal').removeClass('hidden');
        
        // Reset modal content
        resetModal();
        
        // Show loading state
        $('#modalLoading').show();
        
        console.log('Loading teacher data for ID:', teacherId);
        
        // Fetch teacher data from API (using admin API endpoint)
        $.ajax({
            url: `/admin/api/teacher/profile-enhanced`,
            method: 'GET',
            data: { teacher_id: teacherId },
            success: function(response) {
                console.log('API Response:', response);
                if (response.success && response.teacher) {
                    currentTeacherData = response.teacher;
                    populateModal(response.teacher);
                } else {
                    console.error('Failed to load teacher data:', response.message);
                    showModalError('Failed to load teacher data: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading teacher data:', error);
                console.error('XHR Status:', xhr.status);
                console.error('Response Text:', xhr.responseText);
                showModalError('Error loading teacher data: ' + error);
            },
            complete: function() {
                $('#modalLoading').hide();
            }
        });
    }

    /**
     * Close teacher details modal
     */
    function closeTeacherModal() {
        $('#teacherModal').addClass('hidden');
        currentTeacherData = null;
    }

    /**
     * Reset modal to initial state
     */
    function resetModal() {
        // Hide all tab content
        $('.tab-content').addClass('hidden');
        
        // Show personal tab by default
        $('#personalTab').removeClass('hidden');
        
        // Reset tab buttons
        $('.tab-btn').removeClass('active').addClass('text-gray-500').removeClass('text-gray-900 border-gray-600').addClass('border-transparent');
        $('.tab-btn[data-tab="personal"]').addClass('active').removeClass('text-gray-500').addClass('text-gray-900 border-gray-600').removeClass('border-transparent');
        
        // Reset content
        $('#modalTitle').text('Teacher Details');
        $('#modalSubtitle').text('Loading teacher information...');
    }

    /**
     * Switch between modal tabs
     */
    function switchTab(tabName) {
        // Hide all tab content
        $('.tab-content').addClass('hidden');
        
        // Show selected tab
        $(`#${tabName}Tab`).removeClass('hidden');
        
        // Update tab buttons
        $('.tab-btn').removeClass('active').addClass('text-gray-500').removeClass('text-gray-900 border-gray-600').addClass('border-transparent');
        $(`.tab-btn[data-tab="${tabName}"]`).addClass('active').removeClass('text-gray-500').addClass('text-gray-900 border-gray-600').removeClass('border-transparent');
    }

    /**
     * Show modal error
     */
    function showModalError(message) {
        $('#modalLoading').hide();
        $('.tab-content').addClass('hidden');
        $('#personalTab').removeClass('hidden').html(`
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
                <p class="text-sm text-gray-500">${message}</p>
            </div>
        `);
    }

    /**
     * Generate teacher CV PDF
     */
    function generateTeacherCV(teacherId, teacherName, $button) {
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

        // Use current teacher data if available, otherwise fetch it first
        if (currentTeacherData) {
            generatePDFFromData(currentTeacherData, $button, originalHtml);
        } else {
            // Fetch teacher data first, then generate PDF
            $.ajax({
                url: `/admin/api/teacher/profile-enhanced`,
                method: 'GET',
                data: { teacher_id: teacherId },
                success: function(response) {
                    if (response.success && response.teacher) {
                        currentTeacherData = response.teacher;
                        generatePDFFromData(response.teacher, $button, originalHtml);
                    } else {
                        console.error('Failed to load teacher data for PDF:', response.message);
                        $button.html('<i class="fas fa-times mr-2"></i>Error');
                        setTimeout(() => {
                            $button.html(originalHtml);
                        }, 2000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading teacher data for PDF:', error);
                    $button.html('<i class="fas fa-times mr-2"></i>Error');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                }
            });
        }
    }

    /**
     * Generate PDF from teacher data using server-side API
     */
    function generatePDFFromData(teacherData, $button, originalHtml) {
        console.log('🔄 Generating enhanced CV PDF for:', teacherData.name);

        // Call the enhanced PDF generation API
        $.ajax({
            url: '/admin/api/generate-enhanced-cv-pdf',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                teacherId: teacherData.id || teacherData.user_id,
                teacherData: teacherData
            }),
            success: function(response) {
                if (response.success && response.url) {
                    console.log('✅ PDF generated successfully:', response.url);

                    // Open PDF in new browser tab
                    const pdfUrl = response.url;
                    window.open(pdfUrl, '_blank');

                    $button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                } else {
                    console.error('❌ PDF generation failed:', response.message);
                    $button.html('<i class="fas fa-times mr-2"></i>PDF Error');
                    setTimeout(() => {
                        $button.html(originalHtml);
                    }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Error generating PDF:', error);
                console.error('Response:', xhr.responseText);
                $button.html('<i class="fas fa-times mr-2"></i>PDF Error');
                setTimeout(() => {
                    $button.html(originalHtml);
                }, 2000);
            }
        });
    }

    /**
     * Open export modal
     */
    function openExportModal() {
        console.log('📊 Opening teacher export modal...');
        // Implementation for export modal would go here
        alert('Export functionality will be implemented soon.');
    }

    /**
     * Populate modal with teacher data
     */
    function populateModal(data) {
        console.log('Populating modal with data:', data);

        // Update modal title and general info
        const teacherName = data.displayName || data.name || data.full_name || 'Teacher';
        const designation = data.designation || 'Teacher';
        const department = data.department || 'Academic Department';

        $('#modalTitle').text(`${teacherName} - Details`);
        $('#modalSubtitle').text(`${designation} • ${department}`);

        // Update teacher general info section
        const initials = teacherName.split(' ').map(n => n[0]).join('').toUpperCase();
        $('#teacherInitials').text(initials);
        $('#teacherName').text(teacherName);
        $('#teacherDesignation').text(`${designation} • ${department}`);
        $('#teacherContact').text(`${data.email || 'No email'} • ${data.phone || 'No phone'}`);
        $('#employeeId').text(data.employee_id || 'No ID');
        $('#experienceInfo').text(`${data.total_experience_years || 0} years experience`);

        // Update status badge
        if (data.is_active) {
            $('#statusBadge').removeClass('bg-red-100 text-red-800').addClass('bg-green-100 text-green-800').text('Active');
        } else {
            $('#statusBadge').removeClass('bg-green-100 text-green-800').addClass('bg-red-100 text-red-800').text('Inactive');
        }

        // Populate personal information tab
        populatePersonalTab(data);

        // Populate score cards
        populateScoreCards(data);

        // Populate other tabs with enhanced data
        populateEducationTab(data);
        populateExperienceTab(data);
        populateSkillsTab(data);
        populateAchievementsTab(data);
    }

    /**
     * Populate score cards with profile completion and rating data
     */
    function populateScoreCards(data) {
        console.log('📊 Populating score cards with data:', {
            profile_completion: data.profile_completion_percentage,
            rating_score: data.rating_score
        });

        // Profile Completion Score Card
        const profileCompletion = data.profile_completion_percentage || 0;
        const profileCompletionColor = getProgressColor(profileCompletion);

        $('#profileCompletionValue').text(`${profileCompletion}%`);
        $('#profileCompletionCircle').attr('style', `--progress: ${profileCompletion}`);
        $('#profileCompletionCircle .progress-text').text(`${profileCompletion}%`);
        $('#profileCompletionCircle').removeClass('success warning danger info primary').addClass(profileCompletionColor);

        // Update description based on completion level
        let profileDescription = 'Complete your profile';
        if (profileCompletion >= 90) {
            profileDescription = 'Excellent profile completion';
        } else if (profileCompletion >= 75) {
            profileDescription = 'Good profile completion';
        } else if (profileCompletion >= 50) {
            profileDescription = 'Moderate profile completion';
        } else {
            profileDescription = 'Needs profile completion';
        }
        $('#profileCompletionDescription').text(profileDescription);

        // Rating Score Card
        const ratingScore = data.rating_score || 0;
        const ratingColor = getProgressColor(ratingScore);

        $('#ratingScoreValue').text(`${ratingScore}%`);
        $('#ratingScoreCircle').attr('style', `--progress: ${ratingScore}`);
        $('#ratingScoreCircle .progress-text').text(`${ratingScore}%`);
        $('#ratingScoreCircle').removeClass('success warning danger info primary').addClass(ratingColor);

        // Update description based on rating level
        let ratingDescription = 'Performance evaluation';
        if (ratingScore >= 90) {
            ratingDescription = 'Outstanding performance';
        } else if (ratingScore >= 75) {
            ratingDescription = 'Excellent performance';
        } else if (ratingScore >= 60) {
            ratingDescription = 'Good performance';
        } else if (ratingScore >= 40) {
            ratingDescription = 'Satisfactory performance';
        } else {
            ratingDescription = 'Needs improvement';
        }
        $('#ratingScoreDescription').text(ratingDescription);

        // Add animation to the circular progress
        setTimeout(() => {
            $('#profileCompletionCircle, #ratingScoreCircle').addClass('animate');
        }, 100);
    }

    /**
     * Get progress color class based on percentage
     */
    function getProgressColor(percentage) {
        if (percentage >= 80) return 'success';
        if (percentage >= 60) return 'info';
        if (percentage >= 40) return 'warning';
        return 'danger';
    }

    /**
     * Populate personal information tab
     */
    function populatePersonalTab(data) {
        $('#modalDateOfBirth').text(data.date_of_birth || '-');
        $('#modalGender').text(data.gender || '-');
        $('#modalUsername').text(data.username || '-');
        $('#modalJoiningDate').text(data.joining_date || '-');
        $('#modalEmergencyContact').text(data.emergency_contact || '-');
        $('#modalAddress').text(data.address || '-');
        $('#modalOfficeLocation').text(data.office_location || '-');
        $('#modalLanguages').text(data.languages_known || '-');
    }

    /**
     * Populate education tab
     */
    function populateEducationTab(data) {
        const $content = $('#modalEducationContent');

        // Check for enhanced education timeline data first
        if (data.educationTimeline && data.educationTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.educationTimeline.forEach(edu => {
                html += `
                    <div class="border-l-4 border-gray-300 pl-4">
                        <h5 class="font-semibold text-gray-900">${edu.title || 'Qualification'}</h5>
                        <p class="text-sm text-gray-600">${edu.institution || 'Institution'}</p>
                        <div class="text-xs text-gray-500 mt-1">
                            <span>${edu.year || 'Year'}</span>
                            ${edu.percentage ? ` • ${edu.percentage}%` : ''}
                            ${edu.grade ? ` • Grade: ${edu.grade}` : ''}
                            ${edu.cgpa ? ` • CGPA: ${edu.cgpa}` : ''}
                        </div>
                        ${edu.specialization ? `<p class="text-xs text-gray-600 mt-1">Specialization: ${edu.specialization}</p>` : ''}
                        ${edu.board ? `<p class="text-xs text-gray-600">Board/University: ${edu.board}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No education data available</p>');
        }
    }

    /**
     * Populate experience tab
     */
    function populateExperienceTab(data) {
        const $content = $('#modalExperienceContent');

        // Check for enhanced experience timeline data first
        if (data.experienceTimeline && data.experienceTimeline.length > 0) {
            let html = '<div class="space-y-4">';
            data.experienceTimeline.forEach(exp => {
                const borderColor = exp.isCurrent ? 'border-blue-400' : 'border-gray-300';
                html += `
                    <div class="border-l-4 ${borderColor} pl-4">
                        <div class="flex items-center gap-2">
                            <h5 class="font-semibold text-gray-900">${exp.title || 'Position'}</h5>
                            ${exp.isCurrent ? '<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Current</span>' : ''}
                        </div>
                        <p class="text-sm text-gray-600">${exp.institution || 'Organization'}</p>
                        <p class="text-xs text-gray-500">${exp.duration || 'Duration'}</p>
                        ${exp.description ? `<p class="text-sm text-gray-700 mt-2">${exp.description}</p>` : ''}
                    </div>
                `;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No experience data available</p>');
        }
    }

    /**
     * Populate skills tab
     */
    function populateSkillsTab(data) {
        const $skillsContent = $('#modalSkillsContent');
        const $certificationsContent = $('#modalCertificationsContent');

        // Skills
        if (data.skillsByCategory && Object.keys(data.skillsByCategory).length > 0) {
            let skillsHtml = '<div class="space-y-4">';
            Object.entries(data.skillsByCategory).forEach(([category, skills]) => {
                skillsHtml += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-2 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="flex flex-wrap gap-2">
                `;
                skills.forEach(skill => {
                    skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill.name || skill}</span>`;
                });
                skillsHtml += `</div></div>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else if (data.special_skills) {
            const skills = data.special_skills.split(',').map(s => s.trim());
            let skillsHtml = '<div class="flex flex-wrap gap-2">';
            skills.forEach(skill => {
                skillsHtml += `<span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">${skill}</span>`;
            });
            skillsHtml += '</div>';
            $skillsContent.html(skillsHtml);
        } else {
            $skillsContent.html('<p class="text-sm text-gray-500">No skills data available</p>');
        }

        // Certifications
        if (data.certifications && data.certifications.length > 0) {
            let certHtml = '<div class="space-y-3">';
            data.certifications.forEach(cert => {
                certHtml += `
                    <div class="border-l-4 border-gray-200 pl-3">
                        <p class="font-semibold text-gray-900">${cert.name || 'Certification'}</p>
                        <p class="text-xs text-gray-600">${cert.issuer || 'Issuer'}</p>
                        <p class="text-xs text-gray-500">${cert.issueDate ? new Date(cert.issueDate).toLocaleDateString() : 'Date not specified'}</p>
                    </div>
                `;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else if (data.professional_certifications) {
            const certs = data.professional_certifications.split(',').map(c => c.trim());
            let certHtml = '<div class="space-y-2">';
            certs.forEach(cert => {
                certHtml += `<div class="text-sm"><p class="font-semibold text-gray-900">${cert}</p></div>`;
            });
            certHtml += '</div>';
            $certificationsContent.html(certHtml);
        } else {
            $certificationsContent.html('<p class="text-sm text-gray-500">No certifications available</p>');
        }
    }

    /**
     * Populate achievements tab
     */
    function populateAchievementsTab(data) {
        const $content = $('#modalAchievementsContent');

        if (data.achievementsByCategory && Object.keys(data.achievementsByCategory).length > 0) {
            let html = '<div class="space-y-6">';
            Object.entries(data.achievementsByCategory).forEach(([category, achievements]) => {
                html += `
                    <div>
                        <h5 class="font-semibold text-gray-900 mb-3 capitalize">${category.replace('_', ' ')}</h5>
                        <div class="space-y-3">
                `;
                achievements.forEach(achievement => {
                    html += `
                        <div class="border-l-4 border-gray-300 pl-4">
                            <h6 class="font-semibold text-gray-900">${achievement.title || 'Achievement'}</h6>
                            <p class="text-sm text-gray-600">${achievement.description || 'Description'}</p>
                            <p class="text-xs text-gray-500">${achievement.date ? new Date(achievement.date).toLocaleDateString() : 'Date not specified'}</p>
                        </div>
                    `;
                });
                html += `</div></div>`;
            });
            html += '</div>';
            $content.html(html);
        } else {
            $content.html('<p class="text-sm text-gray-500">No achievements recorded</p>');
        }
    }
});
