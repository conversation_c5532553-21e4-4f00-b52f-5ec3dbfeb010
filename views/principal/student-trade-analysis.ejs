<%- include('../partials/header') %>

<div class="principal-container">
    <!-- Page Header -->
    <div class="principal-header mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-principal-dark mb-2">
                    <i class="fas fa-chart-network mr-3 text-principal-primary"></i>
                    Student-Subject-Trade Analysis
                </h1>
                <p class="text-principal-silver">Comprehensive overview of student enrollment patterns across trades and subjects</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="exportAnalysis()" class="btn-principal-secondary">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <button onclick="refreshData()" class="btn-principal-primary">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- Trade Distribution Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="lg:col-span-2">
            <div class="principal-card">
                <div class="principal-card-header">
                    <h3 class="principal-card-title">
                        <i class="fas fa-users mr-2"></i>
                        Trade Distribution by Class
                    </h3>
                </div>
                <div class="principal-card-body">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-principal-light">
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Class</th>
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Trade Category</th>
                                    <th class="text-center py-3 px-4 font-semibold text-principal-dark">Students</th>
                                    <th class="text-left py-3 px-4 font-semibold text-principal-dark">Original Trades</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% if (tradeStats && Array.isArray(tradeStats) && tradeStats.length) { %>
                                    <% tradeStats.forEach(function(stat) { %>
                                        <tr class="border-b border-gray-100 hover:bg-principal-light/20">
                                            <td class="py-3 px-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    Class <%= stat.class %>
                                                </span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <span class="font-medium text-principal-dark"><%= stat.trade_category %></span>
                                            </td>
                                            <td class="py-3 px-4 text-center">
                                                <span class="text-2xl font-bold text-principal-primary"><%= stat.student_count %></span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <div class="flex flex-wrap gap-1">
                                                    <% if (stat.original_trades) { %>
                                                        <% stat.original_trades.split(',').forEach(trade => { %>
                                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                                                                <%= trade.trim() %>
                                                            </span>
                                                        <% }); %>
                                                    <% } %>
                                                </div>
                                            </td>
                                        </tr>
                                    <% }); %>
                                <% } else { %>
                                    <tr>
                                        <td colspan="4" class="text-center py-4 text-principal-silver">
                                            No trade statistics available.
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="principal-card">
                <div class="principal-card-header">
                    <h3 class="principal-card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Quick Stats
                    </h3>
                </div>
                <div class="principal-card-body space-y-4">
                    <% 
                      var totalStudents = 0, uniqueTrades = [], classCount = 0;
                      if (tradeStats && tradeStats.length) {
                        totalStudents = tradeStats.reduce(function(sum, s) { return sum + s.student_count; }, 0);
                        uniqueTrades = Array.from(new Set(tradeStats.map(function(s){ return s.trade_category; })));
                        classCount = Array.from(new Set(tradeStats.map(function(s){ return s.class; }))).length;
                      }
                    %>
                    <div class="text-center p-4 bg-principal-light/30 rounded-lg">
                        <div class="text-3xl font-bold text-principal-primary"><%= totalStudents %></div>
                        <div class="text-sm text-principal-silver">Total Students</div>
                    </div>
                    
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600"><%= uniqueTrades.length %></div>
                        <div class="text-sm text-gray-600">Trade Categories (by Class)</div>
                    </div>
                    
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-3xl font-bold text-green-600"><%= classCount %></div>
                        <div class="text-sm text-gray-600">Active Classes (in Stats)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Enrollment Analysis -->
    <div class="principal-card mb-8">
        <div class="principal-card-header">
            <h3 class="principal-card-title">
                <i class="fas fa-book-open mr-2"></i>
                Subject Enrollment by Trade Category
            </h3>
        </div>
        <div class="principal-card-body">
            <div class="mb-4">
                <div class="flex flex-wrap gap-2">
                    <button onclick="filterByTrade('all')" class="trade-filter-btn active" data-trade="all">
                        All Trades
                    </button>
                    <% if (uniqueTrades && uniqueTrades.length) { %>
                        <% uniqueTrades.forEach(function(tr) { %>
                            <button onclick="filterByTrade('<%= tr %>')" class="trade-filter-btn" data-trade="<%= tr %>">
                                <%= tr %>
                            </button>
                        <% }); %>
                    <% } %>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full" id="subjectTable">
                    <thead>
                        <tr class="border-b border-principal-light">
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Subject Code</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Subject Name</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Stream</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Category</th>
                            <th class="text-left py-3 px-4 font-semibold text-principal-dark">Trade</th>
                            <th class="text-center py-3 px-4 font-semibold text-principal-dark">Enrolled Students</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (typeof subjectStats !== 'undefined' && Array.isArray(subjectStats) && subjectStats.length > 0) { %>
                            <% subjectStats.forEach(subject => { %>
                                <tr class="border-b border-gray-100 hover:bg-principal-light/20 subject-row" data-trade="<%= subject.trade_category %>">
                                    <td class="py-3 px-4">
                                        <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded"><%= subject.subject_code %></span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="font-medium text-principal-dark"><%= subject.subject_name %></span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="text-sm text-principal-silver"><%= subject.stream || 'N/A' %></span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-purple-100 text-purple-800">
                                            <%= subject.subject_category_new || 'General' %>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs 
                                            <% if (subject.trade_category === 'Medical') { %>bg-red-100 text-red-800<% } %>
                                            <% if (subject.trade_category === 'Non Medical') { %>bg-blue-100 text-blue-800<% } %>
                                            <% if (subject.trade_category === 'Commerce') { %>bg-green-100 text-green-800<% } %>
                                            <% if (subject.trade_category === 'Humanities') { %>bg-yellow-100 text-yellow-800<% } %>
                                            <% if (subject.trade_category === 'General') { %>bg-gray-100 text-gray-800<% } %>
                                        ">
                                            <%= subject.trade_category %>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-center">
                                        <span class="text-lg font-bold text-principal-primary"><%= subject.enrolled_students %></span>
                                    </td>
                                </tr>
                            <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="6" class="text-center py-4 px-4 text-principal-silver">No subject enrollment data available.</td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Detailed Student-Subject Mapping -->
    <div class="principal-card">
        <div class="principal-card-header">
            <h3 class="principal-card-title">
                <i class="fas fa-table mr-2"></i>
                Detailed Student-Subject Mapping
            </h3>
            <div class="flex items-center space-x-2">
                <input type="text" id="searchStudents" placeholder="Search students..." 
                       class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                <select id="classFilter" class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option value="">All Classes</option>
                    <option value="10">Class 10</option>
                    <option value="11">Class 11</option>
                    <option value="12">Class 12</option>
                </select>
            </div>
        </div>
        <div class="principal-card-body">
            <div class="overflow-x-auto max-h-96">
                <table class="w-full text-sm" id="studentMappingTable">
                    <thead class="sticky top-0 bg-white">
                        <tr class="border-b border-principal-light">
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Student</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Class</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Trade</th>
                            <th class="text-left py-2 px-3 font-semibold text-principal-dark">Subject</th>
                            <th class="text-center py-2 px-3 font-semibold text-principal-dark">Marks</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (typeof studentSubjectTrades !== 'undefined' && Array.isArray(studentSubjectTrades) && studentSubjectTrades.length > 0) { %>
                            <% studentSubjectTrades.forEach(mapping => { %>
                                <% if (mapping.subject_name) { %>
                                    <tr class="border-b border-gray-50 hover:bg-principal-light/10 student-mapping-row" 
                                        data-class="<%= mapping.class %>" 
                                        data-student="<%= mapping.student_name.toLowerCase() %>">
                                        <td class="py-2 px-3">
                                            <div>
                                                <div class="font-medium text-principal-dark"><%= mapping.student_name %></div>
                                                <div class="text-xs text-principal-silver">ID: <%= mapping.roll_number %></div>
                                            </div>
                                        </td>
                                        <td class="py-2 px-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                                                <%= mapping.class %>-<%= mapping.section %>
                                            </span>
                                        </td>
                                        <td class="py-2 px-3">
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs 
                                                <% if (mapping.trade_category === 'Medical') { %>bg-red-100 text-red-800<% } %>
                                                <% if (mapping.trade_category === 'Non Medical') { %>bg-blue-100 text-blue-800<% } %>
                                                <% if (mapping.trade_category === 'Commerce') { %>bg-green-100 text-green-800<% } %>
                                                <% if (mapping.trade_category === 'Humanities') { %>bg-yellow-100 text-yellow-800<% } %>
                                                <% if (mapping.trade_category === 'General') { %>bg-gray-100 text-gray-800<% } %>
                                            ">
                                                <%= mapping.trade_category %>
                                            </span>
                                        </td>
                                        <td class="py-2 px-3">
                                            <div>
                                                <div class="font-medium"><%= mapping.subject_name %></div>
                                                <div class="text-xs text-gray-500">Code: <%= mapping.subject_code %></div>
                                            </div>
                                        </td>
                                        <td class="py-2 px-3 text-center">
                                            <% if (mapping.total_marks !== null) { %>
                                                <span class="font-medium"><%= mapping.total_marks %>/<%= mapping.max_marks %></span>
                                            <% } else { %>
                                                <span class="text-gray-400">No marks</span>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% } %>
                            <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="5" class="text-center py-4 px-4 text-principal-silver">No student-subject mapping data available.</td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Student Trade Analysis Dashboard -->
    <div class="mb-8">
        <div class="executive-card text-white rounded-xl p-8 shadow-2xl">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Student Trade Analysis</h1>
                    <p class="text-blue-100 text-lg">Comprehensive overview of student distribution by trade</p>
                </div>
                <div class="text-right">
                    <div class="leadership-badge px-4 py-2 rounded-full">
                        <i class="fas fa-chart-pie mr-2"></i>
                        <span class="font-bold">TRADE INSIGHTS</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Students -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-primary">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="p-2 bg-principal-light rounded-lg mr-3">
                            <i class="fas fa-users text-principal-primary text-lg"></i>
                        </div>
                        <span class="text-xs font-semibold text-principal-primary uppercase tracking-wide">Total Students</span>
                    </div>
                    <%
                        let totalStudentCount = 0;
                        if (typeof tradeDistribution !== 'undefined' && Array.isArray(tradeDistribution)) {
                            totalStudentCount = tradeDistribution.reduce((sum, trade) => sum + (trade.student_count || 0), 0);
                        }
                    %>
                    <p class="text-3xl font-bold text-principal-dark"><%= totalStudentCount %></p>
                    <p class="text-sm text-principal-silver">Across <%= tradeDistribution.length %> trades</p>
                </div>
            </div>
        </div>

        <!-- Top Trade -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-secondary">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="p-2 bg-blue-50 rounded-lg mr-3">
                            <i class="fas fa-award text-principal-secondary text-lg"></i>
                        </div>
                        <span class="text-xs font-semibold text-principal-secondary uppercase tracking-wide">Top Trade</span>
                    </div>
                    <%
                        let topTrade = {trade: 'None', student_count: 0};
                        if (typeof tradeDistribution !== 'undefined' && Array.isArray(tradeDistribution) && tradeDistribution.length > 0) {
                            topTrade = tradeDistribution[0];
                        }
                    %>
                    <p class="text-3xl font-bold text-principal-dark"><%= topTrade.trade || 'None' %></p>
                    <p class="text-sm text-principal-silver"><%= topTrade.student_count %> students enrolled</p>
                </div>
            </div>
        </div>

        <!-- Average Students Per Trade -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-principal-accent">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="p-2 bg-yellow-50 rounded-lg mr-3">
                            <i class="fas fa-calculator text-principal-accent text-lg"></i>
                        </div>
                        <span class="text-xs font-semibold text-principal-accent uppercase tracking-wide">Avg. Per Trade</span>
                    </div>
                    <%
                        let avgStudents = 0;
                        if (typeof tradeDistribution !== 'undefined' && Array.isArray(tradeDistribution) && tradeDistribution.length > 0) {
                            avgStudents = Math.round(totalStudentCount / tradeDistribution.length);
                        }
                    %>
                    <p class="text-3xl font-bold text-principal-dark"><%= avgStudents %></p>
                    <p class="text-sm text-principal-silver">Students average</p>
                </div>
            </div>
        </div>

        <!-- Trade Diversity -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="p-2 bg-green-50 rounded-lg mr-3">
                            <i class="fas fa-dice-d20 text-green-500 text-lg"></i>
                        </div>
                        <span class="text-xs font-semibold text-green-500 uppercase tracking-wide">Trade Diversity</span>
                    </div>
                    <p class="text-3xl font-bold text-principal-dark">
                        <%= (typeof tradeDistribution !== 'undefined' && Array.isArray(tradeDistribution)) ? tradeDistribution.length : 0 %>
                    </p>
                    <p class="text-sm text-principal-silver">Active trades</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Trade Distribution Visualizations -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Bar Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
            <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white p-6 rounded-t-xl">
                <div class="flex items-center">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                        <i class="fas fa-chart-bar text-lg"></i>
                    </div>
                    <h2 class="text-xl font-bold">Trade Distribution</h2>
                </div>
            </div>
            <div class="p-6">
                <% if (tradeDistribution && tradeDistribution.length > 0) { %>
                    <div class="space-y-6">
                        <% tradeDistribution.forEach((item, index) => { %>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-700"><%= item.trade || 'Unspecified' %></span>
                                    <span class="text-sm font-medium text-gray-700"><%= item.student_count %> students</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full trade-bar" data-index="<%= index %>"></div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    <%
                                        let percentage = 0;
                                        if (totalStudentCount > 0) {
                                            percentage = Math.round((item.student_count / totalStudentCount) * 100);
                                        }
                                    %>
                                    <%= percentage %>% of total students | Classes: <%= item.classes || 'N/A' %>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } else { %>
                    <div class="text-center py-10">
                        <div class="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-bar text-blue-500 text-2xl"></i>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No data available</h3>
                        <p class="mt-1 text-sm text-gray-500">No student trade data found.</p>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Donut Chart -->
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
            <div class="bg-gradient-to-r from-principal-secondary to-blue-600 text-white p-6 rounded-t-xl">
                <div class="flex items-center">
                    <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                        <i class="fas fa-chart-pie text-lg"></i>
                    </div>
                    <h2 class="text-xl font-bold">Trade Percentage</h2>
                </div>
            </div>
            <div class="p-6">
                <% if (tradeDistribution && tradeDistribution.length > 0) { %>
                    <div class="flex justify-center">
                        <div class="w-64 h-64 relative">
                            <!-- We'll use a placeholder for the donut chart that will be filled with actual data using Chart.js -->
                            <canvas id="tradeChart" width="400" height="400"></canvas>
                        </div>
                    </div>
                    <div class="mt-6 grid grid-cols-2 gap-4">
                        <% tradeDistribution.slice(0, 6).forEach((item, index) => { %>
                            <div class="flex items-center">
                                <!-- Use color from our custom function defined in the script section -->
                                <span class="w-3 h-3 rounded-full mr-2 chart-color-dot" data-index="<%= index %>"></span>
                                <%
                                    let pct = 0;
                                    if (totalStudentCount > 0) {
                                        pct = Math.round((item.student_count / totalStudentCount) * 100) || 0;
                                    }
                                %>
                                <span class="text-xs text-gray-700"><%= item.trade || 'Unspecified' %> (<%= pct %>%)</span>
                            </div>
                        <% }); %>
                    </div>
                <% } else { %>
                    <div class="text-center py-10">
                        <div class="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-pie text-blue-500 text-2xl"></i>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No data available</h3>
                        <p class="mt-1 text-sm text-gray-500">No student trade percentage data found.</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>

    <!-- Trade Data Table -->
    <div class="mb-8">
        <div class="bg-white rounded-xl shadow-lg border border-principal-light">
            <div class="bg-gradient-to-r from-green-600 to-teal-600 text-white p-6 rounded-t-xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                            <i class="fas fa-table text-lg"></i>
                        </div>
                        <h2 class="text-xl font-bold">Detailed Trade Analysis</h2>
                    </div>
                    <a href="#" onclick="exportToCSV(); return false;" class="bg-white text-green-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-50">
                        <i class="fas fa-download mr-2"></i>Export to CSV
                    </a>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student Count</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <% if (tradeDistribution && tradeDistribution.length > 0) { %>
                            <% tradeDistribution.forEach((item, index) => { %>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-8 w-8 bg-principal-light rounded-full flex items-center justify-center mr-3">
                                                <span class="text-principal-primary text-sm font-bold"><%= index + 1 %></span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><%= item.trade || 'Unspecified' %></div>
                                                <div class="text-sm text-gray-500">Grade <%= item.classes ? item.classes.split(',')[0] : 'N/A' %></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><%= item.student_count %> students</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <%
                                            let percentage = 0;
                                            if (totalStudentCount > 0) {
                                                percentage = Math.round((item.student_count / totalStudentCount) * 100);
                                            }
                                        %>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mb-1 max-w-[150px]">
                                            <div class="bg-blue-600 h-2 rounded-full trade-table-bar" data-index="<%= index %>"></div>
                                        </div>
                                        <span class="text-xs text-gray-600"><%= percentage %>%</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <% if (item.classes) { %>
                                                <% const classesList = item.classes.split(',') %>
                                                <div class="flex flex-wrap gap-1">
                                                    <% classesList.forEach(cls => { %>
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            <%= cls %>
                                                        </span>
                                                    <% }) %>
                                                </div>
                                            <% } else { %>
                                                <span class="text-gray-500">No classes assigned</span>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/principal/students?trade=<%= encodeURIComponent(item.trade || '') %>" class="text-indigo-600 hover:text-indigo-900">
                                            View Students <i class="fas fa-chevron-right ml-1"></i>
                                        </a>
                                    </td>
                                </tr>
                            <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="5" class="px-6 py-10 text-center text-gray-500">
                                    No trade data available
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables for trade data
const tradeData = <%- JSON.stringify(typeof tradeDistribution !== 'undefined' ? tradeDistribution : []) %>;
const totalStudentCount = <%= (typeof totalStudentCount !== 'undefined') ? totalStudentCount : 0 %>;

// Trade filtering functionality
function filterByTrade(trade) {
    const rows = document.querySelectorAll('.subject-row');
    const buttons = document.querySelectorAll('.trade-filter-btn');

    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.trade === trade) {
            btn.classList.add('active');
        }
    });

    // Filter rows
    rows.forEach(row => {
        if (trade === 'all' || row.dataset.trade === trade) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Search functionality
document.getElementById('searchStudents').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('.student-mapping-row');
    
    rows.forEach(row => {
        const studentName = row.dataset.student;
        if (studentName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Class filtering
document.getElementById('classFilter').addEventListener('change', function(e) {
    const selectedClass = e.target.value;
    const rows = document.querySelectorAll('.student-mapping-row');
    
    rows.forEach(row => {
        if (!selectedClass || row.dataset.class === selectedClass) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Export functionality
function exportAnalysis() {
    // Implementation for exporting the analysis data
    alert('Export functionality will be implemented');
}

// Refresh functionality
function refreshData() {
    window.location.reload();
}

// Helper function to get chart colors
function getChartColor(index) {
    const colors = [
        '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
        '#8b5cf6', '#ec4899', '#06b6d4', '#14b8a6',
        '#f97316', '#6366f1', '#84cc16', '#0ea5e9'
    ];
    return colors[index % colors.length];
}

// Export to CSV function
function exportToCSV() {
    if (!tradeData || tradeData.length === 0) {
        alert('No data to export');
        return;
    }

    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Trade,Student Count,Percentage,Classes\n";

    tradeData.forEach(item => {
        const percentage = Math.round((item.student_count / totalStudentCount) * 100);
        const row = [
            `"${item.trade || 'Unspecified'}"`,
            item.student_count,
            `${percentage}%`,
            `"${item.classes || 'N/A'}"`
        ].join(',');
        csvContent += row + "\n";
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "trade_distribution_" + new Date().toISOString().slice(0, 10) + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Initialize donut chart if data is available
document.addEventListener('DOMContentLoaded', function () {
    const tradeData = <%- JSON.stringify(typeof tradeDistribution !== 'undefined' ? tradeDistribution : []) %>;
    const totalStudents = <%= (typeof totalStudentCount !== 'undefined') ? totalStudentCount : 0 %>;

    if (!tradeData || tradeData.length === 0 || !document.getElementById('tradeChart')) return;

    // Set widths for trade bars
    document.querySelectorAll('.trade-bar').forEach(bar => {
        const index = parseInt(bar.dataset.index);
        const item = tradeData[index];
        if (item) {
            const percentage = Math.round((item.student_count / totalStudents) * 100);
            bar.style.width = `${percentage}%`;
        }
    });

    // Set background colors for chart color dots
    document.querySelectorAll('.chart-color-dot').forEach(dot => {
        const index = parseInt(dot.dataset.index);
        dot.style.backgroundColor = getChartColor(index);
    });

    // Set widths for trade table bars
    document.querySelectorAll('.trade-table-bar').forEach(bar => {
        const index = parseInt(bar.dataset.index);
        const item = tradeData[index];
        if (item) {
            const percentage = Math.round((item.student_count / totalStudents) * 100);
            bar.style.width = `${percentage}%`;
        }
    });

    // Initialize Chart.js donut chart
    if (document.getElementById('tradeChart')) {
        const labels = tradeData.map(item => item.trade || 'Unspecified');
        const data = tradeData.map(item => item.student_count);
        const backgroundColors = tradeData.map((_, index) => getChartColor(index));
        
        new Chart(document.getElementById('tradeChart'), {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${context.label}: ${value} students (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    }
});
</script>

<style>
.trade-filter-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #D1D5DB;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.trade-filter-btn:hover {
    background-color: #F9FAFB;
}

.trade-filter-btn.active {
    background-color: var(--principal-primary, #1E40AF);
    color: #FFFFFF;
    border-color: var(--principal-primary, #1E40AF);
}
</style>

<%- include('../partials/footer') %>
