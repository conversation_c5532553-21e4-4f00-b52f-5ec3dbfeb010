<div class="container">
  <h1>Subject Overview</h1>
  <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
    <form action="/principal/subject-overview" method="GET" class="flex flex-wrap items-center gap-4 w-full md:w-auto">
      <div class="flex-grow">
        <label for="session" class="sr-only">Academic Session</label>
        <select name="session" id="session" class="form-select block w-full rounded-md border-gray-300 shadow-sm focus:border-principal-primary focus:ring focus:ring-principal-primary focus:ring-opacity-50" onchange="this.form.submit()">
          <% sessions.forEach(s => { %>
            <option value="<%= s %>" <%= filters.session === s ? 'selected' : '' %>><%= s %></option>
          <% }); %>
        </select>
      </div>
      <div class="flex-grow">
        <label for="search" class="sr-only">Search Subjects</label>
        <input type="text" name="search" id="search" placeholder="Search by code or name..." class="form-input block w-full rounded-md border-gray-300 shadow-sm focus:border-principal-primary focus:ring focus:ring-principal-primary focus:ring-opacity-50" value="<%= filters.search %>">
      </div>
      <div>
        <label for="limit" class="sr-only">Records per page</label>
        <select name="limit" id="limit" class="form-select block w-full rounded-md border-gray-300 shadow-sm focus:border-principal-primary focus:ring focus:ring-principal-primary focus:ring-opacity-50" onchange="this.form.submit()">
          <option value="10" <%= filters.limit == 10 ? 'selected' : '' %>>10</option>
          <option value="25" <%= filters.limit == 25 ? 'selected' : '' %>>25</option>
          <option value="50" <%= filters.limit == 50 ? 'selected' : '' %>>50</option>
          <option value="100" <%= filters.limit == 100 ? 'selected' : '' %>>100</option>
        </select>
      </div>
      <button type="submit" class="px-4 py-2 bg-principal-primary text-white rounded-md hover:bg-principal-hover focus:outline-none focus:ring-2 focus:ring-principal-primary focus:ring-opacity-50">Apply Filters</button>
    </form>

    <!-- Pagination (moved to top right) -->
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
      <a href="?page=<%= pagination.currentPage - 1 %>&limit=<%= filters.limit %>&session=<%= filters.session %>&search=<%= filters.search %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 <%= !pagination.hasPrev ? 'pointer-events-none opacity-50' : '' %>">
        <span class="sr-only">Previous</span>
        <i class="fas fa-chevron-left"></i>
      </a>
      <% for (let i = 1; i <= pagination.totalPages; i++) { %>
        <a href="?page=<%= i %>&limit=<%= filters.limit %>&session=<%= filters.session %>&search=<%= filters.search %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 <%= i === pagination.currentPage ? 'bg-principal-primary text-white hover:bg-principal-primary z-10' : '' %>">
          <%= i %>
        </a>
      <% } %>
      <a href="?page=<%= pagination.currentPage + 1 %>&limit=<%= filters.limit %>&session=<%= filters.session %>&search=<%= filters.search %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 <%= !pagination.hasNext ? 'pointer-events-none opacity-50' : '' %>">
        <span class="sr-only">Next</span>
        <i class="fas fa-chevron-right"></i>
      </a>
    </nav>
  </div>

  <div class="bg-white shadow-md rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <div class="max-h-96 overflow-y-auto principal-scrollbar"> <!-- Added max-h-96 and overflow-y-auto -->
        <table class="min-w-full table-auto">
          <thead class="bg-principal-light text-principal-primary uppercase text-sm leading-normal sticky top-0 z-10">
            <tr>
              <th class="py-3 px-6 text-left border-b border-gray-300">Code</th>
              <th class="py-3 px-6 text-left border-b border-gray-300">Name</th>
              <th class="py-3 px-6 text-left border-b border-gray-300">Max Theory Marks</th>
              <th class="py-3 px-6 text-left border-b border-gray-300">Max Practical Marks</th>
              <th class="py-3 px-6 text-left border-b border-gray-300">Max CCE Marks</th>
              <th class="py-3 px-6 text-left border-b border-gray-300">Subject Type</th>
            </tr>
          </thead>
          <tbody class="text-gray-700 text-sm font-light">
            <% if (subjects && subjects.length > 0) { %>
              <% subjects.forEach(subject => { %>
                <tr class="border-b border-gray-200 hover:bg-gray-100">
                  <td class="py-3 px-6 text-left whitespace-nowrap"><%= subject.code %></td>
                  <td class="py-3 px-6 text-left"><%= subject.name %></td>
                  <td class="py-3 px-6 text-left"><%= subject.max_theory_marks !== null ? subject.max_theory_marks : 'N/A' %></td>
                  <td class="py-3 px-6 text-left"><%= subject.max_practical_marks !== null ? subject.max_practical_marks : 'N/A' %></td>
                  <td class="py-3 px-6 text-left"><%= subject.max_cce_marks !== null ? subject.max_cce_marks : 'N/A' %></td>
                  <td class="py-3 px-6 text-left"><%= subject.subject_classification || 'N/A' %></td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No subjects found for this session with the current filters.</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Pagination Summary (moved to top left) -->
  <div class="mt-4 text-sm text-gray-600">
    Showing <%= (pagination.currentPage - 1) * pagination.limit + 1 %> to <%= Math.min(pagination.currentPage * pagination.limit, pagination.totalSubjects) %> of <%= pagination.totalSubjects %> entries
  </div>
</div>