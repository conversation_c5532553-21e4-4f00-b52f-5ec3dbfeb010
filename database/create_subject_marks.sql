CREATE TABLE `subject_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `academic_session` varchar(20) NOT NULL,
  `max_theory_marks` int(11) DEFAULT 0,
  `max_practical_marks` int(11) DEFAULT 0,
  `max_cce_marks` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_session_unique` (`subject_id`,`academic_session`),
  CONSTRAINT `fk_subject_marks_subject_id` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;